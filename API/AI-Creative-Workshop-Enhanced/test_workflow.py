#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI协作创意工坊工作流配置
"""

import yaml

def test_workflow():
    """测试工作流配置"""
    
    # 读取YAML文件
    with open('AI协作创意工坊.yml', 'r', encoding='utf-8') as f:
        workflow = yaml.safe_load(f)
    
    print("🎨 AI协作创意工坊工作流测试")
    print("=" * 50)
    
    # 检查基本结构
    print("✅ 基本结构检查:")
    print(f"  - 应用名称: {workflow['app']['name']}")
    print(f"  - 模式: {workflow['app']['mode']}")
    print(f"  - 版本: {workflow['version']}")
    
    # 检查节点
    nodes = workflow['workflow']['graph']['nodes']
    print(f"\n✅ 节点检查 (共{len(nodes)}个):")
    
    node_types = {}
    for node in nodes:
        node_type = node['data']['type']
        node_id = node['id']
        node_title = node['data'].get('title', 'N/A')
        
        if node_type not in node_types:
            node_types[node_type] = []
        node_types[node_type].append((node_id, node_title))
        
        print(f"  - {node_type}: {node_title} (ID: {node_id})")
    
    # 检查边连接
    edges = workflow['workflow']['graph']['edges']
    print(f"\n✅ 边连接检查 (共{len(edges)}个):")
    
    for edge in edges:
        source = edge['source']
        target = edge['target']
        source_handle = edge.get('sourceHandle', 'source')
        print(f"  - {source} --[{source_handle}]--> {target}")
    
    # 检查if-else节点配置
    print(f"\n✅ IF-ELSE节点检查:")
    for node in nodes:
        if node['data']['type'] == 'if-else':
            cases = node['data']['cases']
            print(f"  - 节点ID: {node['id']}")
            print(f"  - 分支数量: {len(cases)}")
            for case in cases:
                case_id = case['case_id']
                conditions = case['conditions']
                print(f"    * {case_id}: {len(conditions)}个条件")
                for condition in conditions:
                    if 'value' in condition:
                        print(f"      - 包含 '{condition['value']}'")
    
    # 检查参数提取器
    print(f"\n✅ 参数提取器检查:")
    for node in nodes:
        if node['data']['type'] == 'parameter-extractor':
            params = node['data']['parameters']
            print(f"  - 节点ID: {node['id']}")
            print(f"  - 参数数量: {len(params)}")
            for param in params:
                print(f"    * {param['name']}: {param['description']}")
    
    # 检查新增的高级节点
    print(f"\n🔧 高级节点检查:")
    advanced_nodes = ['tool', 'code', 'template-transform']
    for node_type in advanced_nodes:
        count = len(node_types.get(node_type, []))
        print(f"  - {node_type}: {count}个")
        for node_id, node_title in node_types.get(node_type, []):
            print(f"    * {node_title} (ID: {node_id})")

    # 检查工作流路径
    print(f"\n🛤️ 工作流路径分析:")
    print("  创意生成路径:")
    print("    开始 → 参数提取器 → IF-ELSE → 创意生成LLM → 网络搜索 → 数据处理 → 模板转换 → 市场分析LLM → 创意分析结果")
    print("  AI协作路径:")
    print("    开始 → 参数提取器 → IF-ELSE → AI协作LLM → 协作分析器 → AI协作结果")

    print(f"\n🎯 增强版AI协作创意工坊测试完成!")
    print(f"✨ 新增功能:")
    print(f"  - 🔍 实时网络搜索市场案例")
    print(f"  - 🤖 JavaScript代码处理数据")
    print(f"  - 📋 模板转换格式化输出")
    print(f"  - 📊 结构化分析AI协作结果")
    print(f"  - 🎨 更丰富的展示效果")
    return True

if __name__ == "__main__":
    test_workflow()
