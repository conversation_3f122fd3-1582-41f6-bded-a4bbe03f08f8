app:
  description: 'AI协作创意工坊是一个基于AI的创意生成和协作平台，提供创意对战、AI协作、市场预测等多种创意工具，帮助用户进行创意思维的拓展和深化。'
  icon: 🎨
  icon_background: '#FF6B6B'
  mode: advanced-chat
  name: AI协作创意工坊
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: hjlarry/database:0.0.6@534bc26cf5bc4ff6b5557457452287ccc71f00eef9378784c4f43ca49954ca2f
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      - .PDF
      - .TXT
      - .MD
      allowed_file_types:
      - image
      - document
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: true
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 5
    opening_statement: '🎨 欢迎来到AI协作创意工坊！\n\n我是您的创意助手，可以帮助您：\n\n💡 **创意生成** - 根据您的想法生成创新创意\n🤖 **AI协作** - 多种AI角色与您协作创意\n⚔️ **创意对战** - 与AI进行创意PK挑战\n📊 **市场预测** - 分析创意的市场潜力\n🎯 **创意优化** - 改进和完善您的创意\n\n请告诉我您想要探索的创意方向，或者选择一个功能开始吧！'
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - '帮我生成一个智能家居的创意想法'
    - '我想与AI进行创意对战，主题是环保创新'
    - '分析我的创意在市场上的潜力如何'
    - '用不同的AI角色帮我完善这个创意'
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: parameter-extractor
      id: 1751956708955-source-1751956727594-target
      source: '1751956708955'
      sourceHandle: source
      target: '1751956727594'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: parameter-extractor
        targetType: if-else
      id: 1751956727594-source-1751957133814-target
      source: '1751956727594'
      sourceHandle: source
      target: '1751957133814'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1751957133814-source-1751958308882-target
      source: '1751957133814'
      sourceHandle: idea_generation
      target: '1751958308882'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1751957133814-source-1751958434294-target
      source: '1751957133814'
      sourceHandle: collaboration
      target: '1751958434294'
      targetHandle: target
      type: custom
      zIndex: 0

    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 1751958308882-source-1751958400000-target
      source: '1751958308882'
      sourceHandle: source
      target: '1751958400000'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: code
      id: 1751958400000-source-1751958450000-target
      source: '1751958400000'
      sourceHandle: source
      target: '1751958450000'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: template-transform
      id: 1751958450000-source-1751958480000-target
      source: '1751958450000'
      sourceHandle: source
      target: '1751958480000'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: llm
      id: 1751958480000-source-1751958500000-target
      source: '1751958480000'
      sourceHandle: source
      target: '1751958500000'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1751958500000-source-1751958600000-target
      source: '1751958500000'
      sourceHandle: source
      target: '1751958600000'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: 1751958434294-source-1751958650000-target
      source: '1751958434294'
      sourceHandle: source
      target: '1751958650000'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: answer
      id: 1751958650000-source-1751958700000-target
      source: '1751958650000'
      sourceHandle: source
      target: '1751958700000'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: '工作流开始节点'
        selected: false
        title: 开始
        type: start
        variables: []
      height: 53
      id: '1751956708955'
      position:
        x: 100
        y: 300
      positionAbsolute:
        x: 100
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '提取用户需求中的关键参数'
        instruction: |
          分析用户输入，提取创意相关的关键信息。

          特别注意：
          - 如果用户提到"协作"、"合作"、"讨论"、"角色扮演"等词汇，function_type设为"AI协作"
          - 否则默认function_type设为"创意生成"

          请提取以下信息：
          - idea_type: 创意类型
          - theme: 主题领域
          - objective: 具体目标
          - preferences: 用户偏好
          - function_type: 功能类型（AI协作 或 创意生成）
        model:
          completion_params:
            temperature: 0.3
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        parameters:
        - description: '创意类型（如：产品设计、服务创新、技术方案等）'
          name: idea_type
          required: true
          type: string
        - description: '创意主题或领域'
          name: theme
          required: true
          type: string
        - description: '用户的具体需求或目标'
          name: objective
          required: true
          type: string
        - description: '用户偏好或特殊要求'
          name: preferences
          required: false
          type: string
        - description: '功能类型（创意生成、AI协作、创意对战、市场分析）'
          name: function_type
          required: true
          type: string
        query:
        - sys
        - query
        reasoning_mode: prompt
        selected: false
        title: 参数提取器
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 95
      id: '1751956727594'
      position:
        x: 400
        y: 300
      positionAbsolute:
        x: 400
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: collaboration
          conditions:
          - comparison_operator: contains
            id: condition1
            value: '协作'
            variable_selector:
            - '1751956727594'
            - function_type
          logical_operator: and
        - case_id: idea_generation
          conditions: []
          logical_operator: and
        desc: '根据功能类型选择不同的处理路径'
        selected: false
        title: 功能分支
        type: if-else
      height: 126
      id: '1751957133814'
      position:
        x: 700
        y: 300
      positionAbsolute:
        x: 700
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: '生成创新创意'
        model:
          completion_params:
            temperature: 0.8
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是一个专业的创意生成助手，擅长根据用户需求生成创新、实用的创意想法。
            
            请根据以下信息生成详细的创意方案：
            - 创意类型：{{#1751956727594.idea_type#}}
            - 主题领域：{{#1751956727594.theme#}}
            - 目标需求：{{#1751956727594.objective#}}
            - 用户偏好：{{#1751956727594.preferences#}}
            
            生成的创意应该包含：
            1. 创意标题
            2. 核心概念描述
            3. 实现方案
            4. 创新亮点
            5. 应用场景
            6. 潜在价值
        - id: user-prompt
          role: user
          text: '{{#sys.query#}}'
        selected: false
        title: 创意生成LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 95
      id: '1751958308882'
      position:
        x: 1000
        y: 200
      positionAbsolute:
        x: 1000
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 'AI协作模式处理'
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是AI协作创意工坊的多角色AI助手，可以扮演不同的角色与用户协作：
            
            🤖 **挑战者**：找出想法的潜在问题和改进空间
            🌟 **梦想家**：天马行空地扩展可能性
            🔧 **实践者**：提供具体实施方案和技术细节
            🔗 **连接者**：找出与其他领域的关联和融合机会
            ✨ **简化师**：用最简单的方式重新表达和优化
            
            根据用户需求选择合适的角色，提供专业的协作建议。
            
            用户需求：
            - 创意类型：{{#1751956727594.idea_type#}}
            - 主题：{{#1751956727594.theme#}}
            - 目标：{{#1751956727594.objective#}}
            - 偏好：{{#1751956727594.preferences#}}
        - id: user-prompt
          role: user
          text: '{{#sys.query#}}'
        selected: false
        title: AI协作LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 95
      id: '1751958434294'
      position:
        x: 1000
        y: 400
      positionAbsolute:
        x: 1000
        y: 400
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: |
          function main({collaboration_result}) {
              // 分析AI协作结果，提取关键建议
              const lines = collaboration_result.split('\n');
              let suggestions = [];
              let challenges = [];
              let opportunities = [];

              lines.forEach(line => {
                  if (line.includes('🤖') || line.includes('挑战')) {
                      challenges.push(line.trim());
                  } else if (line.includes('🌟') || line.includes('机会') || line.includes('可能')) {
                      opportunities.push(line.trim());
                  } else if (line.includes('建议') || line.includes('方案')) {
                      suggestions.push(line.trim());
                  }
              });

              return {
                  structured_analysis: {
                      challenges: challenges.slice(0, 3),
                      opportunities: opportunities.slice(0, 3),
                      suggestions: suggestions.slice(0, 3)
                  },
                  summary: `分析了${challenges.length}个挑战点，${opportunities.length}个机会点，${suggestions.length}个建议`
              };
          }
        code_language: javascript
        desc: '结构化分析AI协作结果'
        outputs:
          structured_analysis:
            children: null
            type: object
          summary:
            children: null
            type: string
        selected: false
        title: 协作分析器
        type: code
        variables:
        - value_selector:
          - '1751958434294'
          - text
          value_type: string
          variable: collaboration_result
      height: 53
      id: '1751958650000'
      position:
        x: 1300
        y: 400
      positionAbsolute:
        x: 1300
        y: 400
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '查询创意案例数据库'
        is_team_authorization: true
        output_schema:
          properties:
            result:
              type: string
          type: object
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: The SQL query string.
            zh_Hans: SQL 查询语句。
          label:
            en_US: SQL Query
            zh_Hans: SQL 查询语句
          llm_description: The SQL query string.
          max: null
          min: null
          name: query
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Optional, Filling in this field will overwrite the database connection entered during authorization.
            zh_Hans: 选填，填写后将覆盖授权时填写的数据库连接。
          label:
            en_US: DB URI
            zh_Hans: DB URI
          llm_description: ''
          max: null
          min: null
          name: db_uri
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          config_options: ''
          db_uri: ''
          format: ''
          query: ''
        provider_id: hjlarry/database/database
        provider_name: hjlarry/database/database
        provider_type: builtin
        selected: false
        title: 创意案例数据库查询
        tool_configurations:
          config_options: null
          format: md
        tool_description: 此工具用于在已存在的数据库中执行 SQL 查询。
        tool_label: SQL Execute
        tool_name: sql_execute
        tool_parameters:
          query:
            type: mixed
            value: "SELECT * FROM creative_cases WHERE category = '{{#1751956727594.idea_type#}}' OR theme LIKE '%{{#1751956727594.theme#}}%' LIMIT 5;"
        type: tool
      height: 53
      id: '1751958400000'
      position:
        x: 1300
        y: 200
      positionAbsolute:
        x: 1300
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: |
          function main({db_result, idea_text}) {
              // 处理数据库查询结果，提取关键信息
              let case_insights = "";

              if (db_result && db_result.trim()) {
                  // 数据库结果通常是markdown格式的表格或文本
                  const lines = db_result.split('\n').filter(line => line.trim());
                  case_insights = lines.slice(0, 10).join('\n');

                  if (!case_insights) {
                      case_insights = "暂无相关案例数据";
                  }
              } else {
                  case_insights = "数据库查询无结果";
              }

              return {
                  processed_insights: case_insights,
                  combined_data: `创意内容：\n${idea_text}\n\n案例数据库查询结果：\n${case_insights}`
              };
          }
        code_language: javascript
        desc: '处理数据库查询结果和创意内容'
        outputs:
          processed_insights:
            children: null
            type: string
          combined_data:
            children: null
            type: string
        selected: false
        title: 数据处理器
        type: code
        variables:
        - value_selector:
          - '1751958400000'
          - result
          value_type: string
          variable: db_result
        - value_selector:
          - '1751958308882'
          - text
          value_type: string
          variable: idea_text
      height: 53
      id: '1751958450000'
      position:
        x: 1600
        y: 200
      positionAbsolute:
        x: 1600
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        template: |
          📊 **创意与案例分析综合报告**

          ## 🎯 创意概述
          {{combined_data}}

          ## 📈 案例库洞察
          {{processed_insights}}

          ---
          *数据来源：创意案例数据库*
        title: 报告模板
        type: template-transform
        variables:
        - value_selector:
          - '1751958450000'
          - combined_data
          value_type: string
          variable: combined_data
        - value_selector:
          - '1751958450000'
          - processed_insights
          value_type: string
          variable: processed_insights
      height: 53
      id: '1751958480000'
      position:
        x: 1900
        y: 200
      positionAbsolute:
        x: 1900
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: '市场潜力分析'
        model:
          completion_params:
            temperature: 0.5
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是一个专业的市场分析师，擅长评估创意的商业价值和市场潜力。
            
            请对以下创意和市场调研信息进行全面的市场分析：
            {{#1751958480000.result#}}
            
            分析内容应包括：
            1. **市场潜力评分** (1-10分)
            2. **目标用户群体**
            3. **市场规模估算**
            4. **竞争态势分析**
            5. **风险评估**
            6. **成功关键因素**
            7. **实施建议**
            8. **预期收益**
            
            请提供客观、专业的分析结果。
        - id: user-prompt
          role: user
          text: '请分析这个创意的市场潜力'
        selected: false
        title: 市场分析LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 95
      id: '1751958500000'
      position:
        x: 2200
        y: 200
      positionAbsolute:
        x: 2200
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: |
          🎨 **AI协作创意工坊 - 智能创意分析报告**

          ## 💡 原创创意内容
          {{#1751958308882.text#}}

          ## � 市场调研数据
          {{#1751958480000.result#}}

          ## 📊 专业市场分析
          {{#1751958500000.text#}}
          
          ---
          
          ### 🚀 下一步建议
          
          1. **深化创意**：继续完善创意细节
          2. **原型制作**：制作最小可行产品(MVP)
          3. **市场验证**：进行用户调研和反馈收集
          4. **团队组建**：寻找合适的合作伙伴
          
          ### 💬 继续探索
          
          您可以继续询问：
          - "帮我优化这个创意的实施方案"
          - "分析这个创意的技术可行性"
          - "为这个创意设计商业模式"
          - "与AI进行创意对战来改进想法"
        desc: '输出最终结果'
        selected: false
        title: 创意分析结果
        type: answer
        variables: []
      height: 104
      id: '1751958600000'
      position:
        x: 2500
        y: 200
      positionAbsolute:
        x: 2500
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: |
          🤖 **AI协作创意工坊 - 智能协作分析报告**

          ## 🎯 原始协作内容
          {{#1751958434294.text#}}

          ## 📊 结构化分析
          {{#1751958650000.summary#}}

          ### 🚨 挑战与问题
          {{#1751958650000.structured_analysis.challenges#}}

          ### 🌟 机会与可能性
          {{#1751958650000.structured_analysis.opportunities#}}

          ### 💡 建议与方案
          {{#1751958650000.structured_analysis.suggestions#}}

          ---

          ### 🚀 智能化下一步建议

          1. **深入协作**：选择特定角色进行深度对话
          2. **创意迭代**：基于AI分析优化创意
          3. **多角度分析**：从不同角色视角重新审视
          4. **实施规划**：制定具体的行动计划

          ### 💬 继续协作

          您可以继续询问：
          - "请🤖挑战者找出这个想法的问题"
          - "让🌟梦想家扩展更多可能性"
          - "🔧实践者提供具体实施方案"
          - "🔗连接者找出跨领域机会"
        desc: 'AI协作输出结果'
        selected: false
        title: AI协作结果
        type: answer
        variables: []
      height: 104
      id: '1751958700000'
      position:
        x: 1600
        y: 400
      positionAbsolute:
        x: 1600
        y: 400
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -50
      y: 50
      zoom: 0.8
