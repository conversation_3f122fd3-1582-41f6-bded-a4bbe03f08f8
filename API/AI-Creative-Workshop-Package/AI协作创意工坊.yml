app:
  description: 'AI协作创意工坊是一个基于AI的创意生成和协作平台，提供创意对战、AI协作、市场预测等多种创意工具，帮助用户进行创意思维的拓展和深化。'
  icon: 🎨
  icon_background: '#FF6B6B'
  mode: advanced-chat
  name: AI协作创意工坊
  use_icon_as_answer_icon: false
dependencies: []
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      - .PDF
      - .TXT
      - .MD
      allowed_file_types:
      - image
      - document
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: true
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 5
    opening_statement: '🎨 欢迎来到AI协作创意工坊！\n\n我是您的创意助手，可以帮助您：\n\n💡 **创意生成** - 根据您的想法生成创新创意\n🤖 **AI协作** - 多种AI角色与您协作创意\n⚔️ **创意对战** - 与AI进行创意PK挑战\n📊 **市场预测** - 分析创意的市场潜力\n🎯 **创意优化** - 改进和完善您的创意\n\n请告诉我您想要探索的创意方向，或者选择一个功能开始吧！'
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - '帮我生成一个智能家居的创意想法'
    - '我想与AI进行创意对战，主题是环保创新'
    - '分析我的创意在市场上的潜力如何'
    - '用不同的AI角色帮我完善这个创意'
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: parameter-extractor
      id: start-param-extractor
      source: start
      sourceHandle: source
      target: param-extractor
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: parameter-extractor
        targetType: if-else
      id: param-extractor-if-else
      source: param-extractor
      sourceHandle: source
      target: if-else
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm-idea-generation
      id: if-else-idea-generation
      source: if-else
      sourceHandle: true
      target: llm-idea-generation
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm-collaboration
      id: if-else-collaboration
      source: if-else
      sourceHandle: false
      target: llm-collaboration
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm-idea-generation
        targetType: llm-market-analysis
      id: idea-generation-market-analysis
      source: llm-idea-generation
      sourceHandle: source
      target: llm-market-analysis
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm-market-analysis
        targetType: answer
      id: market-analysis-answer
      source: llm-market-analysis
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm-collaboration
        targetType: answer
      id: collaboration-answer
      source: llm-collaboration
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: '工作流开始节点'
        selected: false
        title: 开始
        type: start
        variables:
        - description: '用户输入的创意需求或问题'
          label: '用户输入'
          max_length: 1000
          options: []
          required: true
          type: paragraph
          variable: user_input
      height: 53
      id: start
      position:
        x: 100
        y: 300
      positionAbsolute:
        x: 100
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '提取用户需求中的关键参数'
        instruction: '分析用户输入，提取创意相关的关键信息，包括创意类型、主题、目标、偏好等'
        model:
          completion_params:
            temperature: 0.3
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        parameters:
        - description: '创意类型（如：产品设计、服务创新、技术方案等）'
          name: idea_type
          required: true
          type: string
        - description: '创意主题或领域'
          name: theme
          required: true
          type: string
        - description: '用户的具体需求或目标'
          name: objective
          required: true
          type: string
        - description: '用户偏好或特殊要求'
          name: preferences
          required: false
          type: string
        - description: '功能类型（创意生成、AI协作、创意对战、市场分析）'
          name: function_type
          required: true
          type: string
        query:
        - sys
        - user_input
        reasoning_mode: prompt
        selected: false
        title: 参数提取器
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 95
      id: param-extractor
      position:
        x: 400
        y: 300
      positionAbsolute:
        x: 400
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: idea_generation
          conditions:
          - comparison_operator: contains
            id: condition1
            value: '创意生成'
            variable_selector:
            - param-extractor
            - function_type
          logical_operator: and
        - case_id: collaboration
          conditions:
          - comparison_operator: contains
            id: condition2
            value: 'AI协作'
            variable_selector:
            - param-extractor
            - function_type
          logical_operator: and
        desc: '根据功能类型选择不同的处理路径'
        selected: false
        title: 功能分支
        type: if-else
      height: 126
      id: if-else
      position:
        x: 700
        y: 300
      positionAbsolute:
        x: 700
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: '生成创新创意'
        model:
          completion_params:
            temperature: 0.8
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是一个专业的创意生成助手，擅长根据用户需求生成创新、实用的创意想法。
            
            请根据以下信息生成详细的创意方案：
            - 创意类型：{{#param-extractor.idea_type#}}
            - 主题领域：{{#param-extractor.theme#}}
            - 目标需求：{{#param-extractor.objective#}}
            - 用户偏好：{{#param-extractor.preferences#}}
            
            生成的创意应该包含：
            1. 创意标题
            2. 核心概念描述
            3. 实现方案
            4. 创新亮点
            5. 应用场景
            6. 潜在价值
        - id: user-prompt
          role: user
          text: '{{#start.user_input#}}'
        selected: false
        title: 创意生成LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 95
      id: llm-idea-generation
      position:
        x: 1000
        y: 200
      positionAbsolute:
        x: 1000
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 'AI协作模式处理'
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是AI协作创意工坊的多角色AI助手，可以扮演不同的角色与用户协作：
            
            🤖 **挑战者**：找出想法的潜在问题和改进空间
            🌟 **梦想家**：天马行空地扩展可能性
            🔧 **实践者**：提供具体实施方案和技术细节
            🔗 **连接者**：找出与其他领域的关联和融合机会
            ✨ **简化师**：用最简单的方式重新表达和优化
            
            根据用户需求选择合适的角色，提供专业的协作建议。
            
            用户需求：
            - 创意类型：{{#param-extractor.idea_type#}}
            - 主题：{{#param-extractor.theme#}}
            - 目标：{{#param-extractor.objective#}}
            - 偏好：{{#param-extractor.preferences#}}
        - id: user-prompt
          role: user
          text: '{{#start.user_input#}}'
        selected: false
        title: AI协作LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 95
      id: llm-collaboration
      position:
        x: 1000
        y: 400
      positionAbsolute:
        x: 1000
        y: 400
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: '市场潜力分析'
        model:
          completion_params:
            temperature: 0.5
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是一个专业的市场分析师，擅长评估创意的商业价值和市场潜力。
            
            请对以下创意进行全面的市场分析：
            {{#llm-idea-generation.text#}}
            
            分析内容应包括：
            1. **市场潜力评分** (1-10分)
            2. **目标用户群体**
            3. **市场规模估算**
            4. **竞争态势分析**
            5. **风险评估**
            6. **成功关键因素**
            7. **实施建议**
            8. **预期收益**
            
            请提供客观、专业的分析结果。
        - id: user-prompt
          role: user
          text: '请分析这个创意的市场潜力'
        selected: false
        title: 市场分析LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 95
      id: llm-market-analysis
      position:
        x: 1300
        y: 200
      positionAbsolute:
        x: 1300
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: |
          🎨 **AI协作创意工坊 - 分析结果**
          
          ## 💡 生成的创意
          {{#llm-idea-generation.text#}}
          
          ## 📊 市场分析报告
          {{#llm-market-analysis.text#}}
          
          ---
          
          ### 🚀 下一步建议
          
          1. **深化创意**：继续完善创意细节
          2. **原型制作**：制作最小可行产品(MVP)
          3. **市场验证**：进行用户调研和反馈收集
          4. **团队组建**：寻找合适的合作伙伴
          
          ### 💬 继续探索
          
          您可以继续询问：
          - "帮我优化这个创意的实施方案"
          - "分析这个创意的技术可行性"
          - "为这个创意设计商业模式"
          - "与AI进行创意对战来改进想法"
        desc: '输出最终结果'
        selected: false
        title: 创意分析结果
        type: answer
        variables: []
      height: 104
      id: answer
      position:
        x: 1600
        y: 300
      positionAbsolute:
        x: 1600
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -50
      y: 50
      zoom: 0.8
