#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建AI协作创意工坊的创意案例数据库
"""

import sqlite3
import os

def create_database():
    """创建创意案例数据库"""
    
    # 数据库文件路径
    db_path = "creative_cases.db"
    
    # 如果数据库已存在，删除它
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"🗑️ 删除已存在的数据库: {db_path}")
    
    # 创建数据库连接
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建创意案例表
    cursor.execute('''
    CREATE TABLE creative_cases (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title VARCHAR(200) NOT NULL,
        category VARCHAR(100) NOT NULL,
        theme VARCHAR(100) NOT NULL,
        description TEXT NOT NULL,
        market_potential INTEGER DEFAULT 0,
        success_factors TEXT,
        implementation_difficulty INTEGER DEFAULT 0,
        target_audience VARCHAR(200),
        revenue_model VARCHAR(200),
        created_date DATE DEFAULT CURRENT_DATE
    )
    ''')
    
    # 插入示例数据
    sample_data = [
        ("智能垃圾分类机器人", "产品设计", "环保科技", "基于AI视觉识别的智能垃圾分类设备，可自动识别并分类各种垃圾类型", 9, "AI技术成熟、环保需求强烈、政策支持", 7, "社区居民、环保机构", "设备销售+维护服务", "2024-01-15"),
        ("虚拟现实学习平台", "教育科技", "在线教育", "沉浸式VR教学平台，提供3D互动学习体验", 8, "技术创新、用户体验佳、市场需求大", 8, "学生、教育机构", "订阅制+内容付费", "2024-01-20"),
        ("AI健康饮食助手", "健康科技", "智能生活", "基于个人健康数据的智能饮食推荐系统", 7, "健康意识提升、数据驱动、个性化服务", 6, "健康关注者、慢病患者", "应用内购买+会员制", "2024-02-01"),
        ("共享办公空间管理系统", "服务创新", "办公协作", "智能化共享办公空间预订和管理平台", 6, "远程办公趋势、空间利用效率", 5, "自由职业者、小企业", "平台佣金+增值服务", "2024-02-10"),
        ("区块链艺术品交易平台", "区块链", "数字艺术", "基于NFT的数字艺术品创作和交易平台", 8, "区块链技术、数字收藏热潮", 9, "艺术家、收藏家", "交易手续费+创作工具费", "2024-02-15"),
        ("智能家居语音控制系统", "物联网", "智能家居", "多语言支持的智能家居语音控制中枢", 9, "IoT普及、语音技术成熟", 7, "家庭用户、智能家居爱好者", "硬件销售+软件订阅", "2024-02-20"),
        ("在线心理健康咨询平台", "健康服务", "心理健康", "专业心理咨询师在线服务平台", 8, "心理健康重视度提升、在线服务便利", 6, "需要心理支持的人群", "咨询费分成+会员制", "2024-03-01"),
        ("AI驱动的个性化新闻推荐", "内容科技", "信息服务", "基于用户兴趣和行为的智能新闻推荐引擎", 7, "信息过载问题、个性化需求", 6, "新闻读者、媒体机构", "广告收入+订阅制", "2024-03-05"),
        ("可持续时尚租赁平台", "可持续发展", "时尚消费", "高端服装租赁和循环利用平台", 7, "环保意识、共享经济", 5, "时尚爱好者、环保人士", "租赁费+会员制", "2024-03-10"),
        ("AI音乐创作助手", "创意科技", "音乐创作", "帮助音乐人进行创作的AI辅助工具", 8, "AI创作技术、音乐产业数字化", 7, "音乐创作者、音乐爱好者", "软件授权+云服务", "2024-03-15"),
        ("智能农业监测系统", "农业科技", "智慧农业", "基于IoT和AI的农作物生长监测和管理系统", 9, "农业现代化需求、技术成熟", 8, "农场主、农业合作社", "设备销售+数据服务", "2024-03-20"),
        ("虚拟试衣间技术", "零售科技", "电商购物", "AR/VR技术实现的在线虚拟试衣体验", 8, "电商发展、用户体验需求", 7, "在线购物者、服装品牌", "技术授权+SaaS服务", "2024-03-25"),
        ("社区互助服务平台", "社会创新", "社区服务", "邻里互助和社区服务对接平台", 6, "社区关系重建、互助文化", 4, "社区居民、服务提供者", "服务佣金+广告收入", "2024-04-01"),
        ("AI法律咨询助手", "法律科技", "法律服务", "智能法律问题解答和文档生成系统", 8, "法律服务需求、AI技术应用", 8, "个人用户、小企业", "咨询费+文档生成费", "2024-04-05"),
        ("碳足迹追踪应用", "环保科技", "可持续发展", "个人和企业碳排放追踪和减排建议应用", 7, "碳中和目标、环保意识", 6, "环保人士、企业", "应用内购买+企业服务", "2024-04-10")
    ]
    
    cursor.executemany('''
    INSERT INTO creative_cases (title, category, theme, description, market_potential, 
                               success_factors, implementation_difficulty, target_audience, 
                               revenue_model, created_date)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', sample_data)
    
    # 提交更改
    conn.commit()
    
    # 验证数据
    cursor.execute("SELECT COUNT(*) FROM creative_cases")
    count = cursor.fetchone()[0]
    print(f"✅ 成功创建数据库: {db_path}")
    print(f"📊 插入了 {count} 条创意案例记录")
    
    # 显示一些示例数据
    cursor.execute("SELECT title, category, theme, market_potential FROM creative_cases LIMIT 5")
    samples = cursor.fetchall()
    print(f"\n📋 示例数据:")
    for title, category, theme, potential in samples:
        print(f"  - {title} ({category}/{theme}) - 市场潜力: {potential}/10")
    
    # 关闭连接
    conn.close()
    
    return db_path

if __name__ == "__main__":
    db_path = create_database()
    print(f"\n🎯 数据库创建完成!")
    print(f"📁 数据库文件位置: {os.path.abspath(db_path)}")
    print(f"🔗 数据库连接字符串: sqlite:///{os.path.abspath(db_path)}")
