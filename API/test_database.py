#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试创意案例数据库查询功能
"""

import sqlite3
import os

def test_database_queries():
    """测试数据库查询功能"""
    
    db_path = "creative_cases.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    print("🔍 测试AI协作创意工坊数据库查询功能")
    print("=" * 50)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 测试1: 模拟工作流查询 - 产品设计类
    print("\n📋 测试1: 查询产品设计类创意")
    query1 = "SELECT * FROM creative_cases WHERE category = '产品设计' OR theme LIKE '%产品%' LIMIT 5;"
    cursor.execute(query1)
    results1 = cursor.fetchall()
    print(f"查询SQL: {query1}")
    print(f"结果数量: {len(results1)}")
    for row in results1:
        print(f"  - {row[1]} ({row[2]}/{row[3]}) - 市场潜力: {row[5]}/10")
    
    # 测试2: 模拟工作流查询 - 科技主题
    print("\n📋 测试2: 查询科技主题创意")
    query2 = "SELECT * FROM creative_cases WHERE category = '科技' OR theme LIKE '%科技%' LIMIT 5;"
    cursor.execute(query2)
    results2 = cursor.fetchall()
    print(f"查询SQL: {query2}")
    print(f"结果数量: {len(results2)}")
    for row in results2:
        print(f"  - {row[1]} ({row[2]}/{row[3]}) - 市场潜力: {row[5]}/10")
    
    # 测试3: 高市场潜力创意
    print("\n📋 测试3: 查询高市场潜力创意")
    query3 = "SELECT title, category, theme, market_potential, success_factors FROM creative_cases WHERE market_potential >= 8 ORDER BY market_potential DESC;"
    cursor.execute(query3)
    results3 = cursor.fetchall()
    print(f"查询SQL: {query3}")
    print(f"结果数量: {len(results3)}")
    for row in results3:
        print(f"  - {row[0]} ({row[1]}/{row[2]}) - 潜力: {row[3]}/10")
        print(f"    成功因素: {row[4]}")
    
    # 测试4: 模拟Dify工作流的动态查询
    print("\n📋 测试4: 模拟Dify工作流动态查询")
    # 模拟参数
    idea_type = "教育科技"
    theme = "在线教育"
    
    query4 = f"SELECT * FROM creative_cases WHERE category = '{idea_type}' OR theme LIKE '%{theme}%' LIMIT 5;"
    cursor.execute(query4)
    results4 = cursor.fetchall()
    print(f"模拟参数: idea_type='{idea_type}', theme='{theme}'")
    print(f"查询SQL: {query4}")
    print(f"结果数量: {len(results4)}")
    for row in results4:
        print(f"  - {row[1]} ({row[2]}/{row[3]})")
        print(f"    描述: {row[4][:100]}...")
        print(f"    目标用户: {row[8]}")
        print(f"    收入模式: {row[9]}")
    
    # 测试5: 统计信息
    print("\n📊 数据库统计信息")
    cursor.execute("SELECT COUNT(*) FROM creative_cases")
    total_count = cursor.fetchone()[0]
    print(f"总记录数: {total_count}")
    
    cursor.execute("SELECT category, COUNT(*) FROM creative_cases GROUP BY category ORDER BY COUNT(*) DESC")
    categories = cursor.fetchall()
    print("类别分布:")
    for category, count in categories:
        print(f"  - {category}: {count}个")
    
    cursor.execute("SELECT AVG(market_potential) FROM creative_cases")
    avg_potential = cursor.fetchone()[0]
    print(f"平均市场潜力: {avg_potential:.1f}/10")
    
    cursor.execute("SELECT AVG(implementation_difficulty) FROM creative_cases")
    avg_difficulty = cursor.fetchone()[0]
    print(f"平均实施难度: {avg_difficulty:.1f}/10")
    
    conn.close()
    
    print(f"\n✅ 数据库测试完成!")
    print(f"🎯 数据库已准备就绪，可用于AI协作创意工坊!")
    
    return True

if __name__ == "__main__":
    test_database_queries()
