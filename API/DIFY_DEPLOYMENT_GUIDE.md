# AI协作创意工坊 - Dify平台部署指南

## 📋 概述

本指南详细说明如何将AI协作创意工坊部署到Dify平台，实现无代码的AI应用部署和管理。

## 🎯 Dify配置文件

### 📄 `AI协作创意工坊.yml` - Dify工作流配置

**位置**: `/AI协作创意工坊.yml`
**作用**: 定义Dify平台上的AI工作流和应用配置

## 🚀 部署步骤

### 第一步：准备Dify环境

#### 1.1 访问Dify平台
- **Dify Cloud**: https://cloud.dify.ai
- **自部署Dify**: 按照官方文档部署本地实例

#### 1.2 创建新应用
1. 登录Dify平台
2. 点击"创建应用"
3. 选择"工作流"类型
4. 输入应用名称："AI协作创意工坊"

### 第二步：导入工作流配置

#### 2.1 导入yml文件
1. 在Dify应用编辑界面
2. 点击"导入"按钮
3. 选择 `AI协作创意工坊.yml` 文件
4. 确认导入配置

#### 2.2 验证配置
- ✅ 检查所有节点正确加载
- ✅ 验证连接线路完整
- ✅ 确认参数配置正确

### 第三步：配置AI模型

#### 3.1 模型提供商设置
在Dify平台配置以下AI模型：

**推荐配置**:
```yaml
主模型: gpt-3.5-turbo (OpenAI)
备用模型: claude-3-sonnet (Anthropic)
国内模型: qwen-turbo (通义千问)
```

#### 3.2 API密钥配置
1. 进入"设置" → "模型提供商"
2. 添加OpenAI API密钥
3. 配置其他模型提供商（可选）

### 第四步：测试和发布

#### 4.1 功能测试
测试以下核心功能：

**创意生成测试**:
```
输入: "帮我生成一个智能家居的创意想法"
预期: 生成详细的创意方案和市场分析
```

**AI协作测试**:
```
输入: "我想与AI进行创意协作，主题是环保创新"
预期: AI扮演不同角色提供协作建议
```

#### 4.2 发布应用
1. 测试通过后点击"发布"
2. 设置应用访问权限
3. 获取应用分享链接

## 🔧 工作流详解

### 📊 工作流架构

```mermaid
graph TD
    A[开始] --> B[参数提取器]
    B --> C[功能分支]
    C -->|创意生成| D[创意生成LLM]
    C -->|AI协作| E[AI协作LLM]
    D --> F[市场分析LLM]
    F --> G[创意分析结果]
    E --> G
    G --> H[结束]
```

### 🎯 核心节点说明

#### 1. 参数提取器
**功能**: 从用户输入中提取关键信息
**提取参数**:
- `idea_type`: 创意类型
- `theme`: 创意主题
- `objective`: 用户目标
- `preferences`: 用户偏好
- `function_type`: 功能类型

#### 2. 功能分支
**功能**: 根据用户需求选择处理路径
**分支条件**:
- 创意生成 → 生成新创意 + 市场分析
- AI协作 → 多角色协作模式

#### 3. 创意生成LLM
**功能**: 生成创新创意方案
**输出内容**:
- 创意标题
- 核心概念描述
- 实现方案
- 创新亮点
- 应用场景
- 潜在价值

#### 4. AI协作LLM
**功能**: 多角色AI协作
**AI角色**:
- 🤖 挑战者：找出问题和改进空间
- 🌟 梦想家：扩展可能性
- 🔧 实践者：提供实施方案
- 🔗 连接者：找出关联机会
- ✨ 简化师：优化表达

#### 5. 市场分析LLM
**功能**: 评估创意市场潜力
**分析内容**:
- 市场潜力评分
- 目标用户群体
- 竞争态势分析
- 风险评估
- 成功关键因素

## 🎨 应用特性

### 🌟 用户体验
- **开场白**: 友好的欢迎信息和功能介绍
- **建议问题**: 预设的常用问题模板
- **文件上传**: 支持图片和文档上传
- **后续建议**: 智能推荐下一步操作

### 🔧 技术特性
- **多模型支持**: 兼容多种AI模型
- **参数化配置**: 灵活的参数提取和处理
- **分支逻辑**: 智能的功能路由
- **模板化输出**: 结构化的结果展示

## 📈 使用场景

### 🎯 创意生成场景
```
用户: "帮我设计一个智能垃圾分类系统"
系统: 
1. 提取参数（产品设计、环保、智能化）
2. 生成创意方案
3. 分析市场潜力
4. 提供实施建议
```

### 🤝 AI协作场景
```
用户: "我有一个在线教育的想法，需要AI帮我完善"
系统:
1. 识别协作需求
2. 选择合适的AI角色
3. 提供多角度建议
4. 优化创意方案
```

### 📊 市场分析场景
```
用户: "分析共享单车项目的市场前景"
系统:
1. 理解分析需求
2. 生成市场报告
3. 评估风险和机会
4. 提供决策建议
```

## 🔧 自定义配置

### 修改AI角色
在 `AI协作LLM` 节点中可以自定义AI角色：

```yaml
prompt_template:
  - role: system
    text: |
      你可以扮演以下角色：
      🎨 **设计师**：关注用户体验和视觉设计
      💼 **商业顾问**：提供商业模式建议
      🔬 **技术专家**：评估技术可行性
      📊 **数据分析师**：提供数据驱动的洞察
```

### 调整分析维度
在 `市场分析LLM` 中可以修改分析维度：

```yaml
分析内容应包括：
1. **技术可行性** (1-10分)
2. **商业价值** (1-10分)
3. **用户需求匹配度** (1-10分)
4. **竞争优势分析**
5. **实施难度评估**
```

## 🚀 高级功能

### 集成外部工具
可以在工作流中添加：
- **搜索工具**: 获取实时市场信息
- **数据库工具**: 查询历史创意数据
- **API工具**: 调用外部分析服务

### 多语言支持
修改prompt模板支持多语言：
```yaml
prompt_template:
  - role: system
    text: |
      Please respond in the user's preferred language.
      支持中文、English、日本語等多种语言。
```

## 📞 技术支持

### 常见问题

#### Q1: 导入yml文件失败
**解决方案**:
1. 检查文件格式是否正确
2. 确认Dify版本兼容性
3. 验证yaml语法正确性

#### Q2: AI模型调用失败
**解决方案**:
1. 检查API密钥配置
2. 确认模型可用性
3. 检查网络连接

#### Q3: 工作流执行错误
**解决方案**:
1. 查看执行日志
2. 检查节点配置
3. 验证数据流向

### 获取帮助
- **Dify官方文档**: https://docs.dify.ai
- **社区论坛**: https://github.com/langgenius/dify
- **技术支持**: 联系Dify技术团队

---

## 🎉 总结

通过Dify平台部署AI协作创意工坊，您可以：

- ✅ **无代码部署**: 无需编程即可部署AI应用
- ✅ **可视化管理**: 直观的工作流编辑界面
- ✅ **多模型支持**: 灵活切换不同AI模型
- ✅ **快速迭代**: 快速调整和优化工作流
- ✅ **易于分享**: 一键生成分享链接

现在您可以在Dify平台上享受专业的AI创意协作服务！🚀
