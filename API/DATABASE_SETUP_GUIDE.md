# 🗄️ AI协作创意工坊数据库配置指南

## 📋 概述

AI协作创意工坊使用SQLite数据库存储创意案例数据，为工作流提供丰富的案例查询功能。

## 🎯 数据库文件

- **文件名**: `creative_cases.db`
- **类型**: SQLite 数据库
- **大小**: 约 50KB
- **记录数**: 15条创意案例

## 📊 数据库结构

### 表: creative_cases

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键，自增 |
| title | VARCHAR(200) | 创意标题 |
| category | VARCHAR(100) | 创意类别 |
| theme | VARCHAR(100) | 主题领域 |
| description | TEXT | 详细描述 |
| market_potential | INTEGER | 市场潜力评分(1-10) |
| success_factors | TEXT | 成功因素 |
| implementation_difficulty | INTEGER | 实施难度(1-10) |
| target_audience | VARCHAR(200) | 目标用户群体 |
| revenue_model | VARCHAR(200) | 收入模式 |
| created_date | DATE | 创建日期 |

## 🚀 部署步骤

### 方法1: 本地SQLite部署 (推荐用于演示)

1. **上传数据库文件**
   ```bash
   # 将 creative_cases.db 文件上传到服务器
   scp creative_cases.db user@server:/path/to/database/
   ```

2. **在Dify中配置数据库连接**
   - 打开Dify管理界面
   - 进入"工具" -> "数据库工具"
   - 添加新的数据库连接
   - 连接字符串: `sqlite:////absolute/path/to/creative_cases.db`
   - 测试连接确保正常

### 方法2: MySQL/PostgreSQL部署 (推荐用于生产)

1. **导出SQLite数据**
   ```bash
   # 导出为SQL文件
   sqlite3 creative_cases.db .dump > creative_cases.sql
   ```

2. **导入到MySQL**
   ```sql
   CREATE DATABASE creative_workshop;
   USE creative_workshop;
   SOURCE creative_cases.sql;
   ```

3. **配置连接字符串**
   ```
   mysql://username:password@host:port/creative_workshop
   ```

### 方法3: 云数据库部署

1. **使用云服务商**
   - AWS RDS
   - Google Cloud SQL
   - 阿里云RDS
   - 腾讯云数据库

2. **导入数据**
   - 创建数据库实例
   - 导入creative_cases.sql
   - 配置网络访问权限

## 🔧 Dify工作流配置

在AI协作创意工坊.yml中，数据库查询节点已配置为:

```yaml
tool_parameters:
  query:
    type: mixed
    value: "SELECT * FROM creative_cases WHERE category = '{{#1751956727594.idea_type#}}' OR theme LIKE '%{{#1751956727594.theme#}}%' LIMIT 5;"
```

## 📝 示例查询

```sql
-- 查询产品设计类创意
SELECT * FROM creative_cases WHERE category = '产品设计';

-- 查询环保主题创意
SELECT * FROM creative_cases WHERE theme LIKE '%环保%';

-- 查询高市场潜力创意
SELECT * FROM creative_cases WHERE market_potential >= 8;

-- 查询特定目标用户群体
SELECT * FROM creative_cases WHERE target_audience LIKE '%学生%';
```

## 🛠️ 数据库管理

### 添加新案例
```sql
INSERT INTO creative_cases (title, category, theme, description, market_potential, success_factors, implementation_difficulty, target_audience, revenue_model)
VALUES ('新创意标题', '类别', '主题', '描述', 8, '成功因素', 6, '目标用户', '收入模式');
```

### 更新案例信息
```sql
UPDATE creative_cases 
SET market_potential = 9 
WHERE title = '智能垃圾分类机器人';
```

## 🔒 安全建议

1. **数据库访问控制**
   - 设置强密码
   - 限制网络访问
   - 定期备份数据

2. **SQL注入防护**
   - 使用参数化查询
   - 验证输入数据
   - 限制查询权限

## 📍 文件位置建议

```
/opt/dify/databases/
├── creative_cases.db          # SQLite数据库文件
├── backup/                    # 备份目录
│   ├── creative_cases_backup_20240101.db
│   └── creative_cases_backup_20240201.db
└── logs/                      # 日志目录
    └── database_access.log
```

## 🎯 部署检查清单

- [ ] 数据库文件已上传到服务器
- [ ] Dify中数据库连接配置正确
- [ ] 测试查询功能正常
- [ ] 工作流中数据库节点配置正确
- [ ] 数据库访问权限设置合理
- [ ] 备份策略已制定

## 🆘 故障排除

### 常见问题

1. **连接失败**
   - 检查文件路径是否正确
   - 确认文件权限设置
   - 验证连接字符串格式

2. **查询无结果**
   - 检查SQL语法
   - 确认表名和字段名
   - 验证数据是否存在

3. **权限错误**
   - 检查文件读写权限
   - 确认用户访问权限
   - 验证目录权限设置

## 📞 技术支持

如有问题，请检查:
1. 数据库文件完整性
2. 连接字符串配置
3. Dify工具权限设置
4. 服务器网络连接
