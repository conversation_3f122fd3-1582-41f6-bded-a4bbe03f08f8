app:
  description: '基于AI的创意生成和协作平台，提供创意分裂、风格融合、AI协作、创意对战和市场预测等功能'
  icon: 🎨
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: AI创意工坊助手-优化版
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/tongyi:0.0.27@4bb3f3eb6149b01f92aa6038a6bb074cc4224b8c015f54c888e5c5a30fc1ab50
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      enabled: false
    opening_statement: '🎨 欢迎使用AI创意工坊助手！我可以帮助您：

💡 **创意分裂** - 将想法推向极致、反转化、类比化
🎭 **风格融合** - 多种艺术风格的创新组合
🤖 **AI协作** - 不同角色的AI助手协作
⚔️ **创意对战** - 与AI进行创意PK
📊 **市场预测** - 分析创意的商业潜力

请告诉我您的创意想法，我会为您提供专业的分析和建议！'
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - '帮我分析一个智能健康监测设备的创意'
    - '我想设计一个环保的包装解决方案'
    - '为我的餐厅创意提供市场分析'
    text_to_speech:
      enabled: false
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: parameter-extractor
      id: start-source-param_extractor-target
      source: start
      sourceHandle: source
      target: param_extractor
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: parameter-extractor
        targetType: llm
      id: param_extractor-source-creative_processor-target
      source: param_extractor
      sourceHandle: source
      target: creative_processor
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: template-transform
      id: creative_processor-source-template-target
      source: creative_processor
      sourceHandle: source
      target: template
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: answer
      id: template-source-answer-target
      source: template
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 53
      id: start
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '提取用户输入的创意内容和意图'
        instruction: '从用户输入中提取以下关键信息：
1. 创意内容 - 用户的具体创意想法
2. 处理模式 - 用户希望如何处理这个创意（分裂、协作、预测、融合、对战）
3. 创意类别 - 科技、艺术、商业、环保、健康等
4. 特殊要求 - 用户的特定需求或偏好'
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        parameters:
        - description: '用户的创意内容'
          name: idea_content
          required: true
          type: string
        - description: '用户希望的处理模式：splitter(分裂)、collaborator(协作)、predictor(预测)、mixer(融合)、battle(对战)'
          name: mode
          required: true
          type: string
        - description: '创意的类别标签'
          name: category
          required: false
          type: string
        - description: '用户的特殊要求或偏好'
          name: requirements
          required: false
          type: string
        query:
        - sys
        - query
        reasoning_mode: prompt
        selected: false
        title: 参数提取器
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 95
      id: param_extractor
      position:
        x: 384
        y: 282
      positionAbsolute:
        x: 384
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: system_prompt
          role: system
          text: '你是AI创意工坊的专业分析师，根据用户选择的处理模式提供相应的服务。

## 服务模式：

### 1. 💡 创意分裂 (splitter)
将用户的创意进行5种不同的分裂：
- **极端化** - 将想法推向极致
- **反转化** - 探索相反的概念
- **类比化** - 寻找其他领域的灵感
- **简化版** - 找到最简单的实现方式
- **融合版** - 与流行元素结合

### 2. 🤖 AI协作 (collaborator)
扮演以下角色之一：
- **挑战者** - 找出潜在问题和风险
- **梦想家** - 扩展可能性
- **实践者** - 提供实施方案
- **连接者** - 寻找关联
- **简化师** - 优化表达

### 3. 📊 市场预测 (predictor)
提供全面的商业分析：
- 市场潜力评估 (1-10分)
- 可行性分析
- 创新指数
- 风险评估
- 发展建议

### 4. 🎭 风格融合 (mixer)
分析并推荐风格组合：
- 赛博朋克、极简主义、复古未来等
- 详细的视觉效果描述
- 实现技术方案

### 5. ⚔️ 创意对战 (battle)
创建竞争方案：
- 分析用户创意
- 提出AI竞争方案
- 多维度对比评分

请根据用户选择的模式提供专业、详细的分析。'
        - id: user_prompt
          role: user
          text: '用户需求：
创意内容：{{#param_extractor.idea_content#}}
处理模式：{{#param_extractor.mode#}}
类别：{{#param_extractor.category#}}
特殊要求：{{#param_extractor.requirements#}}

请根据用户选择的处理模式（{{#param_extractor.mode#}}）提供相应的创意分析和建议。'
        selected: false
        title: 创意处理器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 95
      id: creative_processor
      position:
        x: 688
        y: 282
      positionAbsolute:
        x: 688
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '格式化输出'
        template: '# 🎨 AI创意工坊分析报告

## 📋 创意概述
**原始创意：** {{#param_extractor.idea_content#}}
**处理模式：** {{#param_extractor.mode#}}
**创意类别：** {{#param_extractor.category#}}
**特殊要求：** {{#param_extractor.requirements#}}

---

## 🔍 分析结果

{{#creative_processor.text#}}

---

## 💡 后续建议

基于以上分析，建议您：
1. 🎯 **优先考虑** 最具可行性的实施方案
2. 🚀 **深入研究** 市场机会和技术实现
3. 🤝 **寻找合作伙伴** 补充必要的技能和资源
4. 📈 **制定路线图** 分阶段推进项目发展
5. 🔄 **持续迭代** 根据反馈优化创意方案'
        selected: false
        title: 结果格式化
        type: template-transform
        variables: []
      height: 54
      id: template
      position:
        x: 992
        y: 282
      positionAbsolute:
        x: 992
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#template.output#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 104
      id: answer
      position:
        x: 1296
        y: 282
      positionAbsolute:
        x: 1296
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 0
      y: 0
      zoom: 1