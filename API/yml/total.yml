app:
  description: '基于AI的创意生成和协作平台，提供创意分裂、风格融合、AI协作、创意对战和市场预测等功能'
  icon: 🎨
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: AI创意工坊助手
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/tongyi:0.0.27@4bb3f3eb6149b01f92aa6038a6bb074cc4224b8c015f54c888e5c5a30fc1ab50
kind: app
version: 0.3.0
workflow:
  conversation_variables:
  - description: '用户的创意历史记录'
    id: creativity_history
    name: creativity_history
    selector:
    - conversation
    - creativity_history
    value: []
    value_type: array[string]
  - description: '当前处理模式'
    id: current_mode
    name: current_mode
    selector:
    - conversation
    - current_mode
    value: ''
    value_type: string
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: true
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: '🎨 欢迎使用AI创意工坊助手！我可以帮助您：

💡 **创意分裂** - 将想法推向极致、反转化、类比化
🎭 **风格融合** - 多种艺术风格的创新组合
🤖 **AI协作** - 5种不同角色的AI助手协作
⚔️ **创意对战** - 与AI进行创意PK
📊 **市场预测** - 分析创意的商业潜力

请告诉我您的创意想法，我会为您提供专业的分析和建议！'
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - '帮我分析一个智能健康监测设备的创意'
    - '我想设计一个环保的包装解决方案'
    - '为我的餐厅创意提供市场分析'
    - '帮我对比几个不同的创意方案'
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: parameter-extractor
      id: start-source-param_extractor-target
      source: start
      sourceHandle: source
      target: param_extractor
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: parameter-extractor
        targetType: question-classifier
      id: param_extractor-source-classifier-target
      source: param_extractor
      sourceHandle: source
      target: classifier
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: llm
      id: classifier-source-idea_splitter-target
      source: classifier
      sourceHandle: '1'
      target: idea_splitter
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: llm
      id: classifier-source-ai_collaborator-target
      source: classifier
      sourceHandle: '2'
      target: ai_collaborator
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: llm
      id: classifier-source-market_predictor-target
      source: classifier
      sourceHandle: '3'
      target: market_predictor
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: llm
      id: classifier-source-style_mixer-target
      source: classifier
      sourceHandle: '4'
      target: style_mixer
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: llm
      id: classifier-source-battle_creator-target
      source: classifier
      sourceHandle: '5'
      target: battle_creator
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: template-transform
      id: idea_splitter-source-format_output-target
      source: idea_splitter
      sourceHandle: source
      target: format_output
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: template-transform
      id: ai_collaborator-source-format_output-target
      source: ai_collaborator
      sourceHandle: source
      target: format_output
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: template-transform
      id: market_predictor-source-format_output-target
      source: market_predictor
      sourceHandle: source
      target: format_output
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: template-transform
      id: style_mixer-source-format_output-target
      source: style_mixer
      sourceHandle: source
      target: format_output
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: template-transform
      id: battle_creator-source-format_output-target
      source: battle_creator
      sourceHandle: source
      target: format_output
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: assigner
      id: format_output-source-save_history-target
      source: format_output
      sourceHandle: source
      target: save_history
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: assigner
        targetType: answer
      id: save_history-source-answer-target
      source: save_history
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: '开始节点'
        selected: false
        title: 开始
        type: start
        variables: []
      height: 53
      id: start
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '提取用户输入的创意内容和意图'
        instruction: '从用户输入中提取以下关键信息：
1. 创意内容 - 用户的具体创意想法
2. 处理模式 - 用户希望如何处理这个创意（分裂、协作、预测、融合、对战）
3. 创意类别 - 科技、艺术、商业、环保、健康等
4. 特殊要求 - 用户的特定需求或偏好'
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        parameters:
        - description: '用户的创意内容'
          name: idea_content
          required: true
          type: string
        - description: '用户希望的处理模式：splitter(分裂)、collaborator(协作)、predictor(预测)、mixer(融合)、battle(对战)'
          name: mode
          required: true
          type: string
        - description: '创意的类别标签'
          name: category
          required: false
          type: string
        - description: '用户的特殊要求或偏好'
          name: requirements
          required: false
          type: string
        selected: false
        title: 参数提取器
        type: parameter-extractor
        variables: []
      height: 54
      id: param_extractor
      position:
        x: 384
        y: 282
      positionAbsolute:
        x: 384
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        classes:
        - id: '1'
          name: 创意分裂
        - id: '2'
          name: AI协作
        - id: '3'
          name: 市场预测
        - id: '4'
          name: 风格融合
        - id: '5'
          name: 创意对战
        desc: '根据用户意图选择处理模式'
        instruction: '根据提取的处理模式和用户意图，选择相应的创意处理类型：

1. 创意分裂 - 用户想要拓展、分解或变化创意
2. AI协作 - 用户需要AI助手的建议和协作
3. 市场预测 - 用户想了解创意的商业潜力
4. 风格融合 - 用户希望混合不同风格
5. 创意对战 - 用户想要与AI进行创意PK

参数信息：
- 创意内容：{{#param_extractor.idea_content#}}
- 处理模式：{{#param_extractor.mode#}}
- 类别：{{#param_extractor.category#}}
- 特殊要求：{{#param_extractor.requirements#}}'
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        query_variable_selector:
        - param_extractor
        - mode
        selected: false
        title: 意图分类器
        type: question-classifier
        variables: []
      height: 54
      id: classifier
      position:
        x: 688
        y: 282
      positionAbsolute:
        x: 688
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: '创意分裂处理'
        model:
          completion_params:
            temperature: 0.8
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: creative_system
          role: system
          text: '你是一个创意分裂专家，擅长从不同角度拓展和深化创意。你需要将用户的创意进行5种不同的分裂：

1. **极端化** - 将想法推向极致，考虑最大化的可能性
2. **反转化** - 探索完全相反的概念或方法
3. **类比化** - 寻找其他领域的相似应用或灵感
4. **简化版** - 找到最简单、最基础的实现方式
5. **融合版** - 与当前流行元素或趋势结合

每种分裂都要提供：
- 具体的创意内容
- 实现的关键步骤
- 潜在的优缺点
- 适用场景'
        - id: creative_user
          role: user
          text: '请对以下创意进行分裂分析：

创意内容：{{#param_extractor.idea_content#}}
类别：{{#param_extractor.category#}}
特殊要求：{{#param_extractor.requirements#}}

请按照5种分裂方式进行详细分析，并为每种方式提供可行的实施建议。'
        selected: false
        title: 创意分裂器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 54
      id: idea_splitter
      position:
        x: 992
        y: 82
      positionAbsolute:
        x: 992
        y: 82
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 'AI协作处理'
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: collab_system
          role: system
          text: '你是AI协作专家，可以扮演5种不同角色来协助用户：

🤖 **挑战者** - 找出想法的潜在问题、风险和改进空间
🌟 **梦想家** - 天马行空地扩展可能性，激发创新思维
🔧 **实践者** - 提供具体的实施方案和技术细节
🔗 **连接者** - 找出与其他领域、技术或概念的关联
✨ **简化师** - 用最简单的方式重新表达和优化

根据用户的创意内容，选择最合适的角色，并提供详细的协作建议。'
        - id: collab_user
          role: user
          text: '请对以下创意进行AI协作分析：

创意内容：{{#param_extractor.idea_content#}}
类别：{{#param_extractor.category#}}
特殊要求：{{#param_extractor.requirements#}}

请选择最合适的AI角色，并提供：
1. 角色选择理由
2. 详细的协作建议
3. 具体的下一步行动计划
4. 潜在的协作伙伴或资源推荐'
        selected: false
        title: AI协作助手
        type: llm
        variables: []
        vision:
          enabled: false
      height: 54
      id: ai_collaborator
      position:
        x: 992
        y: 182
      positionAbsolute:
        x: 992
        y: 182
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: '市场预测分析'
        model:
          completion_params:
            temperature: 0.5
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: market_system
          role: system
          text: '你是市场预测专家，需要对创意进行全面的商业分析。你的分析包括：

📊 **市场潜力评估** (1-10分)
- 市场规模和增长潜力
- 目标用户群体规模
- 市场成熟度和竞争激烈程度

🎯 **可行性分析** (1-10分)
- 技术实现难度
- 资金需求评估
- 团队能力要求

💡 **创新指数** (1-10分)
- 创意的独特性
- 与现有解决方案的差异化
- 颠覆性潜力

⚡ **风险评估**
- 技术风险
- 市场风险
- 竞争风险
- 法律法规风险

🚀 **发展建议**
- 最佳市场进入时机
- 推荐的发展路径
- 关键成功因素'
        - id: market_user
          role: user
          text: '请对以下创意进行市场预测分析：

创意内容：{{#param_extractor.idea_content#}}
类别：{{#param_extractor.category#}}
特殊要求：{{#param_extractor.requirements#}}

请提供详细的市场分析报告，包括：
1. 市场潜力评分和分析
2. 可行性评估
3. 创新指数评价
4. 风险评估列表
5. 具体的发展建议和时间规划'
        selected: false
        title: 市场预测器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 54
      id: market_predictor
      position:
        x: 992
        y: 282
      positionAbsolute:
        x: 992
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: '风格融合处理'
        model:
          completion_params:
            temperature: 0.8
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: style_system
          role: system
          text: '你是风格融合专家，擅长将不同的艺术风格、设计理念和创意元素进行创新性组合。

预设风格库：
🌆 **赛博朋克** - 高科技与低生活的反差美学
🔲 **极简主义** - 简洁、纯粹的设计理念
🚀 **复古未来** - 复古元素与未来科技的结合
🌿 **生物拟态** - 模仿自然生物的设计
🎨 **波普艺术** - 流行文化的艺术表现
🏛️ **新古典** - 传统与现代的优雅融合
🌈 **色彩主义** - 以色彩为主导的设计语言

你需要：
1. 分析创意的核心特征
2. 推荐3-5种适合的风格组合
3. 详细描述融合后的视觉效果
4. 提供实现的技术方案'
        - id: style_user
          role: user
          text: '请对以下创意进行风格融合分析：

创意内容：{{#param_extractor.idea_content#}}
类别：{{#param_extractor.category#}}
特殊要求：{{#param_extractor.requirements#}}

请提供：
1. 创意的核心设计元素分析
2. 推荐的风格组合方案（至少3种）
3. 每种组合的详细视觉描述
4. 风格兼容性评估
5. 实现的技术建议'
        selected: false
        title: 风格融合器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 54
      id: style_mixer
      position:
        x: 992
        y: 382
      positionAbsolute:
        x: 992
        y: 382
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: '创意对战模式'
        model:
          completion_params:
            temperature: 0.9
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: battle_system
          role: system
          text: '你是创意对战专家，需要创建一个与用户创意竞争的AI创意方案。

对战规则：
1. 基于用户创意的同一主题或领域
2. 提供一个具有竞争力的替代方案
3. 突出AI创意的独特优势
4. 保持公平竞争的原则

评估维度：
💡 **创新性** - 创意的新颖程度
🎯 **实用性** - 解决问题的有效性
🚀 **可行性** - 实现的可能性
💰 **商业价值** - 市场潜力和盈利能力
🌟 **吸引力** - 对用户的吸引程度

你需要：
1. 分析用户创意的核心价值
2. 提出竞争性的AI创意方案
3. 对比两个方案的优劣
4. 提供公正的评估结果'
        - id: battle_user
          role: user
          text: '创意对战开始！

用户创意：{{#param_extractor.idea_content#}}
类别：{{#param_extractor.category#}}
特殊要求：{{#param_extractor.requirements#}}

请提供：
1. 用户创意的核心分析
2. AI竞争创意方案
3. 两个方案的详细对比
4. 各项评估维度的评分
5. 对战结果和获胜理由'
        selected: false
        title: 创意对战器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 54
      id: battle_creator
      position:
        x: 992
        y: 482
      positionAbsolute:
        x: 992
        y: 482
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '格式化输出结果'
        template: '# 🎨 AI创意工坊分析报告

## 📋 创意概述
**原始创意：** {{#param_extractor.idea_content#}}
**处理模式：** {{#param_extractor.mode#}}
**创意类别：** {{#param_extractor.category#}}
**特殊要求：** {{#param_extractor.requirements#}}

---

## 🔍 分析结果

{{#idea_splitter.text#}}{{#ai_collaborator.text#}}{{#market_predictor.text#}}{{#style_mixer.text#}}{{#battle_creator.text#}}

---

## 💡 后续建议

基于以上分析，建议您：
1. 🎯 **优先考虑** 最具可行性的实施方案
2. 🚀 **深入研究** 市场机会和技术实现
3. 🤝 **寻找合作伙伴** 补充必要的技能和资源
4. 📈 **制定路线图** 分阶段推进项目发展
5. 🔄 **持续迭代** 根据反馈优化创意方案

如需进一步分析或不同角度的建议，请告诉我！'
        selected: false
        title: 结果格式化
        type: template-transform
        variables: []
      height: 54
      id: format_output
      position:
        x: 1296
        y: 282
      positionAbsolute:
        x: 1296
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        assigned_variable_selector:
        - conversation
        - creativity_history
        desc: '保存创意处理历史'
        input_variable_selector:
        - format_output
        - output
        selected: false
        title: 保存历史
        type: assigner
        variables: []
        write_mode: append
      height: 54
      id: save_history
      position:
        x: 1600
        y: 282
      positionAbsolute:
        x: 1600
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#format_output.output#}}'
        desc: '输出最终结果'
        selected: false
        title: 回答
        type: answer
        variables: []
      height: 54
      id: answer
      position:
        x: 1904
        y: 282
      positionAbsolute:
        x: 1904
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 0
      y: 0
      zoom: 1