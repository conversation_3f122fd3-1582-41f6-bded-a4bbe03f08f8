#!/usr/bin/env python3
"""
SimplerEnv物体位置处理流程可视化
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

def create_flow_diagram():
    """创建处理流程图"""
    fig, ax = plt.subplots(1, 1, figsize=(14, 16))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 20)
    ax.axis('off')
    
    # 定义颜色
    color_input = '#FFE6CC'
    color_interface = '#D4E6F1'
    color_process = '#D5F4E6'
    color_physics = '#FADBD8'
    color_output = '#E8DAEF'
    
    # 标题
    ax.text(5, 19, 'SimplerEnv Object Position Processing Flow', fontsize=20, ha='center', weight='bold')  #  'SimplerEnv物体位置处理流程'
    
    # 1. 输入层
    input_box = FancyBboxPatch((1, 17), 8, 1.5, 
                               boxstyle="round,pad=0.1", 
                               facecolor=color_input, 
                               edgecolor='black', 
                               linewidth=2)
    ax.add_patch(input_box)
    ax.text(5, 17.75, 'Input: objects + positions', fontsize=12, ha='center', weight='bold')  #  '输入: objects + positions'
    ax.text(5, 17.3, 'objects = ["pepsi_can", "orange", "coke_can", "green_cube_3cm"]', fontsize=9, ha='center')
    ax.text(5, 16.9, 'positions = [(0,0,0), (-0.30,0.15,0), (-0.20,0.25,0), (-0.25,0.10,0)]', fontsize=9, ha='center')
    
    # 2. 接口层
    interface_box = FancyBboxPatch((1, 14.5), 8, 2, 
                                  boxstyle="round,pad=0.1", 
                                  facecolor=color_interface, 
                                  edgecolor='black', 
                                  linewidth=2)
    ax.add_patch(interface_box)
    ax.text(5, 15.7, 'create_scene_with_custom_positions()', fontsize=12, ha='center', weight='bold')
    ax.text(2, 15.2, '• Parameter validation', fontsize=10, ha='left')  #  '• 参数验证'
    ax.text(2, 14.9, '• Z-coordinate processing (z=0 → z=0.89)', fontsize=10, ha='left')  #  '• z坐标处理 (z=0 → z=0.89)'
    ax.text(2, 14.6, '• Create configuration dictionary', fontsize=10, ha='left')  #  '• 创建配置字典'
    
    # 3. 配置层
    config_box = FancyBboxPatch((1, 11.5), 8, 2.5, 
                               boxstyle="round,pad=0.1", 
                               facecolor=color_process, 
                               edgecolor='black', 
                               linewidth=2)
    ax.add_patch(config_box)
    ax.text(5, 13.2, 'main() → create_stable_environment()', fontsize=12, ha='center', weight='bold')
    ax.text(2, 12.7, '• Create StableMultiObjectEnv class', fontsize=10, ha='left')  #  '• 创建StableMultiObjectEnv类'
    ax.text(2, 12.4, '• Inherit GraspSingleCustomInSceneEnv', fontsize=10, ha='left')  #  '• 继承GraspSingleCustomInSceneEnv'
    ax.text(2, 12.1, '• Save object_positions to instance', fontsize=10, ha='left')  #  '• 保存object_positions到实例'
    ax.text(2, 11.8, '• Override _initialize_actors method', fontsize=10, ha='left')  #  '• 重写_initialize_actors方法'
    
    # 4. 物体加载层
    load_box = FancyBboxPatch((1, 8), 8, 3, 
                             boxstyle="round,pad=0.1", 
                             facecolor=color_physics, 
                             edgecolor='black', 
                             linewidth=2)
    ax.add_patch(load_box)
    ax.text(5, 10.3, 'Object Loading (_load_model)', fontsize=12, ha='center', weight='bold')  #  '物体加载 (_load_model)'
    ax.text(2, 9.8, '• Target object: _build_actor_helper(model_id)', fontsize=10, ha='left')  #  '• 目标物体: _build_actor_helper(model_id)'
    ax.text(2, 9.5, '• Distractor objects: Loop to create distractor_objs', fontsize=10, ha='left')  #  '• 干扰物体: 循环创建distractor_objs'
    ax.text(2, 9.2, '• Load collision model: collision.obj', fontsize=10, ha='left')  #  '• 加载碰撞模型: collision.obj'
    ax.text(2, 8.9, '• Load visual model: textured.obj/dae/glb', fontsize=10, ha='left')  #  '• 加载视觉模型: textured.obj/dae/glb'
    ax.text(2, 8.6, '• Set physical properties: density, friction, elasticity', fontsize=10, ha='left')  #  '• 设置物理属性: 密度、摩擦力、弹性'
    ax.text(2, 8.3, '• Build SAPIEN Actor object', fontsize=10, ha='left')  #  '• 构建SAPIEN Actor对象'
    
    # 5. 位置设置层
    position_box = FancyBboxPatch((1, 4), 8, 3.5, 
                                 boxstyle="round,pad=0.1", 
                                 facecolor=color_physics, 
                                 edgecolor='black', 
                                 linewidth=2)
    ax.add_patch(position_box)
    ax.text(5, 6.8, 'Position Setting (_initialize_actors)', fontsize=12, ha='center', weight='bold')  #  '位置设置 (_initialize_actors)'
    ax.text(2, 6.3, '• Collect all objects: [self.obj] + distractor_objs', fontsize=10, ha='left')  #  '• 收集所有物体: [self.obj] + distractor_objs'
    ax.text(2, 6.0, '• Iterate through custom position list', fontsize=10, ha='left')  #  '• 遍历自定义位置列表'
    ax.text(2, 5.7, '• Set object position: obj.set_pose(sapien.Pose([x,y,z]))', fontsize=10, ha='left')  #  '• 设置物体位置: obj.set_pose(sapien.Pose([x,y,z]))'
    ax.text(2, 5.4, '• Reset velocity: set_velocity/angular_velocity', fontsize=10, ha='left')  #  '• 清零速度: set_velocity/angular_velocity'
    ax.text(2, 5.1, '• Set damping: set_damping(0.8, 0.8)', fontsize=10, ha='left')  #  '• 设置阻尼: set_damping(0.8, 0.8)'
    ax.text(2, 4.8, '• Physical stabilization: _settle(0.2)', fontsize=10, ha='left')  #  '• 物理稳定: _settle(0.2)'
    ax.text(2, 4.5, '• Position correction: Check and reset fallen objects', fontsize=10, ha='left')  #  '• 位置修正: 检查并重置掉落物体'
    
    # 6. 渲染输出层
    output_box = FancyBboxPatch((1, 0.5), 8, 3, 
                               boxstyle="round,pad=0.1", 
                               facecolor=color_output, 
                               edgecolor='black', 
                               linewidth=2)
    ax.add_patch(output_box)
    ax.text(5, 2.8, 'Rendering and Output', fontsize=12, ha='center', weight='bold')  #  '渲染与输出'
    ax.text(2, 2.3, '• SAPIEN physical simulation run', fontsize=10, ha='left')  #  '• SAPIEN物理仿真运行'
    ax.text(2, 2.0, '• Camera captures scene (overhead_camera)', fontsize=10, ha='left')  #  '• 相机捕获场景 (overhead_camera)'
    ax.text(2, 1.7, '• Generate RGB image', fontsize=10, ha='left')  #  '• 生成RGB图像'
    ax.text(2, 1.4, '• Save to file: my_experiment_episode1_3rd_view_camera.png', fontsize=10, ha='left')  #  '• 保存到文件: my_experiment_episode1_3rd_view_camera.png'
    ax.text(2, 1.1, '• Return: (success, results, image_paths)', fontsize=10, ha='left')  #  '• 返回: (success, results, image_paths)'
    
    # 添加箭头连接
    arrows = [
        (5, 16.5, 5, 16.0),  # 输入到接口
        (5, 14.5, 5, 14.0),  # 接口到配置
        (5, 11.5, 5, 11.0),  # 配置到加载
        (5, 8.0, 5, 7.5),    # 加载到位置
        (5, 4.0, 5, 3.5),    # 位置到输出
    ]
    
    for x1, y1, x2, y2 in arrows:
        ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                   arrowprops=dict(arrowstyle='->', lw=2, color='black'))
    
    # 添加SAPIEN标注
    sapien_box = FancyBboxPatch((9.5, 5), 1.5, 5, 
                               boxstyle="round,pad=0.1", 
                               facecolor='#F8F9F9', 
                               edgecolor='red', 
                               linewidth=2,
                               linestyle='--')
    ax.add_patch(sapien_box)
    ax.text(10.25, 9.5, 'SAPIEN', fontsize=10, ha='center', color='red', weight='bold')
    ax.text(10.25, 9.1, 'Physics Engine', fontsize=9, ha='center', color='red')  #  '物理引擎'
    ax.text(10.25, 8.5, '• PhysX', fontsize=8, ha='center', color='red')
    ax.text(10.25, 8.2, '• Rigid body dynamics', fontsize=8, ha='center', color='red')  #  '• 刚体动力学'
    ax.text(10.25, 7.9, '• Collision detection', fontsize=8, ha='center', color='red')  #  '• 碰撞检测'
    ax.text(10.25, 7.6, '• Scene rendering', fontsize=8, ha='center', color='red')  #  '• 场景渲染'
    
    # 保存图片
    plt.tight_layout()
    plt.savefig('/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/processing_flow_diagram.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    print("✅ Flowchart saved to: processing_flow_diagram.png")  #  "✅ 流程图已保存到: processing_flow_diagram.png"
    
    # 显示图片
    plt.show()

def create_coordinate_system_diagram():
    """创建坐标系统示意图"""
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 设置坐标轴范围
    ax.set_xlim([-0.5, 0.1])
    ax.set_ylim([0, 0.4])
    ax.set_zlim([0.8, 1.0])
    
    # 绘制桌面
    xx, yy = np.meshgrid(np.linspace(-0.5, 0.1, 10), 
                         np.linspace(0, 0.4, 10))
    zz = np.ones_like(xx) * 0.87
    ax.plot_surface(xx, yy, zz, alpha=0.3, color='brown')
    
    # 绘制物体位置
    objects = [
        ("pepsi_can", (0, 0, 0.89), 'red'),
        ("orange", (-0.30, 0.15, 0.89), 'orange'),
        ("coke_can", (-0.20, 0.25, 0.89), 'black'),
        ("green_cube", (-0.25, 0.10, 0.89), 'green')
    ]
    
    for name, pos, color in objects:
        ax.scatter(*pos, s=200, c=color, alpha=0.8)
        ax.text(pos[0], pos[1], pos[2]+0.02, name, fontsize=8)
    
    # 设置标签
    ax.set_xlabel('X (left ← → right)')  #  'X (左← →右)'
    ax.set_ylabel('Y (far ← → near)')  #  'Y (远← →近)'
    ax.set_zlabel('Z (down ← → up)')  #  'Z (下← →上)'
    ax.set_title('SimplerEnv Coordinate System and Object Positions', fontsize=14)  #  'SimplerEnv坐标系统与物体位置'
    
    # 添加桌面高度标注
    ax.text(-0.5, 0, 0.87, f'Table z=0.87', fontsize=10, color='brown')  #  f'桌面 z=0.87'
    
    # 保存图片
    plt.savefig('/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/coordinate_system_diagram.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    print("✅ Coordinate system diagram saved to: coordinate_system_diagram.png")  #  "✅ 坐标系统图已保存到: coordinate_system_diagram.png"
    
    plt.show()

if __name__ == "__main__":
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    print("🎨 Generating processing flow visualization charts...")  #  "🎨 生成处理流程可视化图表..."
    
    # 生成流程图
    create_flow_diagram()
    
    # 生成坐标系统图
    create_coordinate_system_diagram()
    
    print("\n✅ All charts generated!")  #  "\n✅ 所有图表生成完成！" 