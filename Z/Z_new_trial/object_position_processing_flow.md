# SimplerEnv物体位置处理流程解析

## 概述
本文档详细解析了从输入objects和positions到最终生成画面中对应物体的完整处理流程。

## 处理流程图

```
输入: objects + positions
    ↓
create_scene_with_custom_positions()
    ├── 参数验证
    ├── z坐标处理 (如果z=0，设为0.87+0.02)
    └── 调用 create_scene_advanced()
        ↓
    create_scene_advanced()
        └── 调用 main()
            ↓
        main()
            ├── 处理物体配置
            ├── 创建环境类 (create_stable_environment)
            │   └── 返回 StableMultiObjectEnv 类
            └── 循环创建场景 (num_episodes)
                ↓
            创建环境实例
                ├── EnvClass初始化
                │   ├── 继承自 GraspSingleCustomInSceneEnv
                │   └── 保存 object_positions
                ├── env.reset()
                │   ├── 调用父类reset
                │   └── 触发 _initialize_actors()
                └── 生成图像并保存
```

## 核心步骤详解

### 1. 输入处理 (run_custom.py)
```python
objects = ["pepsi_can", "orange", "coke_can", "green_cube_3cm"]
positions = [
    (0, 0, 0),          # 中心
    (-0.30, 0.15, 0),   # 左下
    (-0.20, 0.25, 0),   # 右上
    (-0.25, 0.10, 0)    # 下方
]
```

### 2. 位置预处理 (create_scene_with_custom_positions)
- 验证objects和positions数量匹配
- 处理z坐标：如果z=0或None，自动设置为桌面高度(0.87) + 0.02
- 创建配置字典传递给下层函数

### 3. 环境类创建 (create_stable_environment)
创建继承自`GraspSingleCustomInSceneEnv`的`StableMultiObjectEnv`类：
- 保存`object_positions`到实例变量
- 重写`_initialize_actors`方法来使用自定义位置

### 4. 物体加载 (_load_model in GraspSingleCustomInSceneEnv)
使用`_build_actor_helper`方法创建物体：
```python
self.obj = self._build_actor_helper(
    model_id,           # 物体模型ID
    self._scene,        # SAPIEN场景
    scale=scale,        # 缩放比例
    density=density,    # 物体密度
    physical_material=material,  # 物理材质
    root_dir=asset_root # 资源根目录
)
```

### 5. Actor构建过程 (_build_actor_helper)
```python
def _build_actor_helper(...):
    builder = scene.create_actor_builder()
    
    # 加载碰撞模型
    collision_file = model_dir / "collision.obj"
    builder.add_multiple_collisions_from_file(
        filename=collision_file,
        scale=[scale] * 3,
        material=physical_material,
        density=density
    )
    
    # 加载视觉模型 (支持.obj/.dae/.glb格式)
    visual_file = model_dir / "textured.obj"  # 或 .dae/.glb
    builder.add_visual_from_file(filename=visual_file, scale=[scale] * 3)
    
    # 构建Actor
    actor = builder.build()
    return actor
```

### 6. 物体位置设置 (_initialize_actors)
```python
def _initialize_actors(self):
    # 收集所有物体
    all_objects = [self.obj] + self.distractor_objs
    
    # 使用自定义位置
    for i, obj in enumerate(all_objects):
        x, y = positions[i][:2]  # 获取x,y坐标
        z = self.scene_table_height + 0.02  # 设置z坐标
        
        # 使用SAPIEN设置物体位置
        obj.set_pose(sapien.Pose([x, y, z], q))
        
        # 清零速度，设置阻尼
        obj.set_velocity(np.zeros(3))
        obj.set_angular_velocity(np.zeros(3))
        obj.set_damping(0.8, 0.8)
        
        # 短暂稳定
        self._settle(0.2)
```

### 7. 物理仿真与渲染
- SAPIEN物理引擎模拟物体稳定
- 通过相机(如overhead_camera)捕获场景
- 生成RGB图像并保存

## 关键技术组件

### SAPIEN物理引擎
- 基于NVIDIA PhysX的物理仿真
- 支持刚体动力学、碰撞检测
- 提供精确的物体位置控制

### 模型资源结构
```
models/
├── pepsi_can/
│   ├── collision.obj    # 碰撞模型
│   ├── textured.obj     # 视觉模型
│   └── texture files    # 纹理文件
├── orange/
└── ...
```

### 坐标系统
- x轴：左(-) 右(+)
- y轴：远(-) 近(+)  
- z轴：下(-) 上(+)
- 桌面高度：z = 0.87

## 总结
整个流程通过以下步骤将输入的objects和positions转换为最终画面：

1. **接口层**：接收用户输入，预处理参数
2. **配置层**：创建环境配置，传递位置信息
3. **环境层**：创建仿真环境，加载物体模型
4. **物理层**：使用SAPIEN设置物体位置，进行物理仿真
5. **渲染层**：通过相机捕获场景，生成最终图像

这个黑箱的核心是SAPIEN物理引擎，它负责：
- 加载3D模型（碰撞体和视觉体）
- 设置物体的物理属性
- 放置物体到指定位置
- 模拟物理稳定过程
- 渲染最终画面 