#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版场景分割分析工具
结合场景定义和分割分析功能，提供简单易用的接口

功能：
1. 创建自定义场景
2. 分析场景中的分割结果
3. 输出分割可视化

使用方法:
python simple_scene_segmentation.py
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import json

# 添加必要的路径
sys.path.append('/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv')
sys.path.append('/home/<USER>/claude/SpatialVLA')

# 导入场景定义模块
from demo_improved_interface import create_scene_with_custom_positions, generate_grid_positions, generate_circle_positions

# 导入SimplerEnv
import simpler_env
from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict


class SimpleSceneSegmentation:
    """简化的场景分割分析器"""
    
    def __init__(self, output_dir: str = None):
        if output_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = f"simple_scene_segmentation_{timestamp}"
        
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"📁 输出目录: {self.output_dir}")
    
    def create_and_analyze_scene(self, 
                               objects: List[str],
                               positions: List[Tuple[float, float, float]],
                               robot_type: str = "google_robot_static",
                               camera_type: str = "overhead_camera",
                               lighting_mode: str = "indoor_bright",
                               task_name: str = "google_robot_pick_coke_can",
                               seed: int = 1234) -> Dict:
        """创建场景并进行分割分析"""
        
        print("🚀 开始场景创建和分割分析")
        print("=" * 50)
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "parameters": {
                "objects": objects,
                "positions": positions,
                "robot_type": robot_type,
                "camera_type": camera_type,
                "lighting_mode": lighting_mode,
                "task_name": task_name,
                "seed": seed
            },
            "scene_creation": {},
            "segmentation_analysis": {}
        }
        
        # 步骤1: 创建场景
        print("\n📍 步骤1: 创建自定义场景")
        scene_success, scene_results, scene_images = create_scene_with_custom_positions(
            objects=objects,
            positions=positions,
            robot_type=robot_type,
            camera_type=camera_type,
            lighting_mode=lighting_mode,
            output_dir=self.output_dir,
            image_prefix="scene_segmentation",
            num_episodes=1,
            verbose=True
        )
        
        results["scene_creation"] = {
            "success": scene_success,
            "results": scene_results,
            "images": scene_images
        }
        
        if not scene_success:
            print("❌ 场景创建失败")
            return results
        
        # 步骤2: 简化的分割分析
        print("\n📍 步骤2: 分析场景分割")
        try:
            segmentation_results = self.analyze_segmentation_simple(
                task_name=task_name,
                seed=seed,
                camera_type=camera_type
            )
            results["segmentation_analysis"] = segmentation_results
            print("✅ 分割分析完成")
        except Exception as e:
            print(f"❌ 分割分析失败: {e}")
            results["segmentation_analysis"] = {"error": str(e)}
        
        # 步骤3: 保存结果
        print("\n📍 步骤3: 保存结果")
        self.save_results(results)
        
        print(f"\n✅ 分析完成! 结果保存在: {self.output_dir}")
        return results
    
    def analyze_segmentation_simple(self, 
                                  task_name: str,
                                  seed: int,
                                  camera_type: str) -> Dict:
        """简化的分割分析"""
        
        print(f"🔍 开始分割分析...")
        print(f"   任务: {task_name}")
        print(f"   种子: {seed}")
        print(f"   相机: {camera_type}")
        
        # 设置相机名称映射
        camera_name_mapping = {
            "overhead_camera": "overhead_camera",
            "base_camera": "base_camera",
            "3rd_view_camera": "3rd_view_camera"
        }
        camera_name = camera_name_mapping.get(camera_type, "overhead_camera")
        
        try:
            # 创建环境
            env = simpler_env.make(
                task_name,
                obs_mode="image",
                camera_names=[camera_name],
                render_mode="rgb_array",
                enable_segmentation=True
            )
            
            # 重置环境
            obs, info = env.reset(seed=seed)
            
            # 获取分割数据
            segmentation_data = obs['image'][camera_name]['Segmentation']
            
            # 分析分割数据
            analysis_results = self.analyze_segmentation_data(segmentation_data, camera_name)
            
            # 获取actors和articulations信息
            actors_info = self.get_actors_info_simple(env)
            articulations_info = self.get_articulations_info_simple(env)
            
            # 创建可视化
            self.create_segmentation_visualization(
                obs, segmentation_data, analysis_results, camera_name
            )
            
            env.close()
            
            return {
                "success": True,
                "camera_name": camera_name,
                "segmentation_analysis": analysis_results,
                "actors_info": actors_info,
                "articulations_info": articulations_info
            }
            
        except Exception as e:
            print(f"❌ 分割分析错误: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "error": str(e)
            }
    
    def analyze_segmentation_data(self, segmentation_data: np.ndarray, camera_name: str) -> Dict:
        """分析分割数据"""
        
        print(f"📊 分析分割数据 (相机: {camera_name})")
        print(f"   数据形状: {segmentation_data.shape}")
        
        # 提取分割ID（第二个通道）
        if len(segmentation_data.shape) == 3 and segmentation_data.shape[2] >= 2:
            seg_ids = segmentation_data[:, :, 1]
        else:
            seg_ids = segmentation_data
        
        # 获取唯一的分割ID
        unique_ids = np.unique(seg_ids)
        print(f"   发现 {len(unique_ids)} 个唯一分割ID: {unique_ids}")
        
        # 统计每个ID的像素数量
        id_pixel_counts = {}
        total_pixels = seg_ids.size
        
        for seg_id in unique_ids:
            pixel_count = np.sum(seg_ids == seg_id)
            percentage = (pixel_count / total_pixels) * 100
            id_pixel_counts[int(seg_id)] = {
                "pixel_count": int(pixel_count),
                "percentage": float(percentage)
            }
        
        # 按像素数量排序
        sorted_ids = sorted(id_pixel_counts.items(), key=lambda x: x[1]["pixel_count"], reverse=True)
        
        print(f"   分割ID按像素数量排序:")
        for seg_id, info in sorted_ids:
            print(f"     ID {seg_id}: {info['pixel_count']} 像素 ({info['percentage']:.2f}%)")
        
        return {
            "unique_ids": unique_ids.tolist(),
            "id_pixel_counts": id_pixel_counts,
            "total_pixels": int(total_pixels),
            "shape": segmentation_data.shape
        }
    
    def get_actors_info_simple(self, env) -> List[Dict]:
        """获取actors信息（简化版）"""
        actors_info = []
        
        try:
            if hasattr(env, 'get_actors'):
                actors = env.get_actors()
                print(f"   发现 {len(actors)} 个actors")
                
                for actor in actors:
                    actor_info = {
                        'id': getattr(actor, 'id', 'unknown'),
                        'name': getattr(actor, 'name', 'unknown'),
                        'type': type(actor).__name__
                    }
                    actors_info.append(actor_info)
                    print(f"     Actor: ID={actor_info['id']}, Name='{actor_info['name']}'")
            else:
                print("   无法获取actors信息")
                
        except Exception as e:
            print(f"   获取actors信息失败: {e}")
        
        return actors_info
    
    def get_articulations_info_simple(self, env) -> List[Dict]:
        """获取articulations信息（简化版）"""
        articulations_info = []
        
        try:
            if hasattr(env, 'get_articulations'):
                articulations = env.get_articulations()
                print(f"   发现 {len(articulations)} 个articulations")
                
                for articulation in articulations:
                    articulation_info = {
                        'id': getattr(articulation, 'id', 'unknown'),
                        'name': getattr(articulation, 'name', 'unknown'),
                        'type': type(articulation).__name__
                    }
                    articulations_info.append(articulation_info)
                    print(f"     Articulation: ID={articulation_info['id']}, Name='{articulation_info['name']}'")
            else:
                print("   无法获取articulations信息")
                
        except Exception as e:
            print(f"   获取articulations信息失败: {e}")
        
        return articulations_info
    
    def create_segmentation_visualization(self, obs, segmentation_data, analysis_results, camera_name):
        """创建分割可视化"""
        
        print(f"🎨 创建分割可视化...")
        
        try:
            # 创建图像
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle(f'场景分割分析 - {camera_name}', fontsize=16)
            
            # 原始RGB图像
            rgb_image = get_image_from_maniskill2_obs_dict(obs, camera_name)
            axes[0, 0].imshow(rgb_image)
            axes[0, 0].set_title('原始RGB图像')
            axes[0, 0].axis('off')
            
            # 分割ID图像
            seg_ids = segmentation_data[:, :, 1] if len(segmentation_data.shape) == 3 else segmentation_data
            axes[0, 1].imshow(seg_ids, cmap='tab20')
            axes[0, 1].set_title('分割ID图像')
            axes[0, 1].axis('off')
            
            # 分割ID统计
            id_pixel_counts = analysis_results['id_pixel_counts']
            ids = list(id_pixel_counts.keys())
            percentages = [info['percentage'] for info in id_pixel_counts.values()]
            
            axes[1, 0].bar(range(len(ids)), percentages)
            axes[1, 0].set_xlabel('分割ID')
            axes[1, 0].set_ylabel('像素百分比 (%)')
            axes[1, 0].set_title('分割ID像素分布')
            axes[1, 0].set_xticks(range(len(ids)))
            axes[1, 0].set_xticklabels([f'ID{id}' for id in ids], rotation=45)
            
            # 分割掩码叠加
            overlay = rgb_image.copy()
            alpha = 0.3
            colors = plt.cm.tab20(np.linspace(0, 1, len(np.unique(seg_ids))))
            
            for i, seg_id in enumerate(np.unique(seg_ids)):
                if seg_id == 0:  # 跳过背景
                    continue
                mask = seg_ids == seg_id
                color = colors[i % len(colors)][:3]
                overlay[mask] = (1 - alpha) * overlay[mask] + alpha * np.array(color[:3]) * 255
            
            axes[1, 1].imshow(overlay.astype(np.uint8))
            axes[1, 1].set_title('分割掩码叠加')
            axes[1, 1].axis('off')
            
            plt.tight_layout()
            
            # 保存可视化
            viz_path = os.path.join(self.output_dir, f"segmentation_visualization_{camera_name}.png")
            plt.savefig(viz_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"   可视化已保存: {viz_path}")
            
        except Exception as e:
            print(f"   创建可视化失败: {e}")
            import traceback
            traceback.print_exc()
    
    def save_results(self, results: Dict):
        """保存结果"""
        try:
            # 保存JSON结果
            json_path = os.path.join(self.output_dir, "segmentation_results.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            # 保存文本报告
            report_path = os.path.join(self.output_dir, "segmentation_report.txt")
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("场景分割分析报告\n")
                f.write("=" * 40 + "\n\n")
                
                f.write(f"分析时间: {results['timestamp']}\n")
                f.write(f"输出目录: {self.output_dir}\n\n")
                
                # 参数
                params = results['parameters']
                f.write("分析参数:\n")
                f.write(f"  物体: {params['objects']}\n")
                f.write(f"  位置: {params['positions']}\n")
                f.write(f"  机器人: {params['robot_type']}\n")
                f.write(f"  相机: {params['camera_type']}\n")
                f.write(f"  光照: {params['lighting_mode']}\n")
                f.write(f"  任务: {params['task_name']}\n\n")
                
                # 场景创建结果
                scene_result = results['scene_creation']
                f.write("场景创建结果:\n")
                f.write(f"  成功: {scene_result['success']}\n")
                if scene_result['success']:
                    f.write(f"  图像: {scene_result['images']}\n")
                f.write("\n")
                
                # 分割分析结果
                seg_result = results['segmentation_analysis']
                f.write("分割分析结果:\n")
                f.write(f"  成功: {seg_result.get('success', False)}\n")
                if seg_result.get('success', False):
                    analysis = seg_result.get('segmentation_analysis', {})
                    f.write(f"  唯一分割ID: {analysis.get('unique_ids', [])}\n")
                    f.write(f"  总像素数: {analysis.get('total_pixels', 0)}\n")
                
                f.write("\n" + "=" * 40 + "\n")
                f.write("报告生成完成\n")
            
            print(f"📄 结果已保存: {json_path}")
            print(f"📋 报告已保存: {report_path}")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")


# 演示函数
def demo_coke_orange_scene():
    """演示可乐和橙子场景"""
    print("🎯 演示: 可乐和橙子场景分割分析")
    print("=" * 50)
    
    analyzer = SimpleSceneSegmentation()
    
    # 定义场景
    objects = ["coke_can", "orange"]
    positions = [
        (-0.25, 0.15, 0),  # 可乐罐
        (-0.22, 0.18, 0),  # 橙子
    ]
    
    # 运行分析
    results = analyzer.create_and_analyze_scene(
        objects=objects,
        positions=positions,
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="indoor_bright",
        task_name="google_robot_pick_coke_can"
    )
    
    return results


def demo_multi_objects_grid():
    """演示多物体网格布局"""
    print("🎯 演示: 多物体网格布局分割分析")
    print("=" * 50)
    
    analyzer = SimpleSceneSegmentation()
    
    # 定义场景
    objects = ["coke_can", "pepsi_can", "orange", "apple"]
    positions = generate_grid_positions(len(objects))
    
    # 运行分析
    results = analyzer.create_and_analyze_scene(
        objects=objects,
        positions=positions,
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="laboratory",
        task_name="google_robot_pick_coke_can"
    )
    
    return results


def demo_circle_layout():
    """演示圆形布局"""
    print("🎯 演示: 圆形布局分割分析")
    print("=" * 50)
    
    analyzer = SimpleSceneSegmentation()
    
    # 定义场景
    objects = ["coke_can", "orange", "apple"]
    positions = generate_circle_positions(len(objects), radius=0.06)
    
    # 运行分析
    results = analyzer.create_and_analyze_scene(
        objects=objects,
        positions=positions,
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="natural",
        task_name="google_robot_pick_coke_can"
    )
    
    return results


def main():
    """主函数"""
    print("🎊 简化版场景分割分析工具")
    print("=" * 60)
    print("功能:")
    print("1. 创建自定义场景")
    print("2. 分析场景分割")
    print("3. 生成可视化结果")
    print()
    
    # 运行演示
    demos = [
        ("可乐和橙子场景", demo_coke_orange_scene),
        ("多物体网格布局", demo_multi_objects_grid),
        ("圆形布局", demo_circle_layout),
    ]
    
    print("📋 可用演示:")
    for i, (name, _) in enumerate(demos, 1):
        print(f"   {i}. {name}")
    
    print("\n🚀 开始运行演示...\n")
    
    results = {}
    
    for demo_name, demo_func in demos:
        print(f"\n{'='*60}")
        print(f"开始运行: {demo_name}")
        print(f"{'='*60}")
        
        try:
            result = demo_func()
            scene_success = result.get("scene_creation", {}).get("success", False)
            seg_success = result.get("segmentation_analysis", {}).get("success", False)
            
            results[demo_name] = {
                "scene_success": scene_success,
                "segmentation_success": seg_success,
                "overall_success": scene_success and seg_success,
                "output_dir": getattr(result, 'output_dir', ''),
                "error": None
            }
            
        except Exception as e:
            print(f"❌ {demo_name} 运行失败: {e}")
            results[demo_name] = {
                "scene_success": False,
                "segmentation_success": False,
                "overall_success": False,
                "output_dir": "",
                "error": str(e)
            }
    
    # 总结
    print("\n" + "="*60)
    print("📊 运行总结:")
    
    total_demos = len(results)
    success_demos = sum(1 for r in results.values() if r["overall_success"])
    
    for demo_name, result in results.items():
        scene_status = "✅" if result["scene_success"] else "❌"
        seg_status = "✅" if result["segmentation_success"] else "❌"
        overall_status = "✅ 完全成功" if result["overall_success"] else "⚠️ 部分成功" if result["scene_success"] else "❌ 失败"
        
        print(f"   {demo_name}: {overall_status}")
        print(f"      场景创建: {scene_status}  分割分析: {seg_status}")
        
        if result["error"]:
            print(f"      错误: {result['error']}")
    
    print(f"\n总计: {success_demos}/{total_demos} 演示完全成功")
    print(f"成功率: {success_demos/total_demos*100:.1f}%")
    
    print("\n🎉 演示完成!")
    print("\n💡 使用方法:")
    print("   1. 创建 SimpleSceneSegmentation 实例")
    print("   2. 定义物体和位置")
    print("   3. 调用 create_and_analyze_scene() 方法")
    print("   4. 查看生成的结果文件和可视化")
    
    print("\n📁 输出文件:")
    print("   • segmentation_results.json - 完整结果")
    print("   • segmentation_report.txt - 分析报告")
    print("   • segmentation_visualization_*.png - 可视化结果")
    print("   • scene_segmentation_*.png - 场景图像")


if __name__ == "__main__":
    main() 