#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
场景分割分析工具
结合场景定义和分割分析功能，在自定义场景中运行分割分析

功能：
1. 使用场景定义接口创建自定义场景
2. 在生成的场景中运行分割分析
3. 输出分割结果和可视化
4. 保存映射结果

使用方法:
python scene_segmentation_analysis.py
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import json

# 添加必要的路径
sys.path.append('/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv')
sys.path.append('/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/X/Data')

# 导入场景定义模块
from demo_improved_interface import create_scene_with_custom_positions, generate_grid_positions, generate_circle_positions

# 导入分割分析模块
from print_segmentation_id_mapping import SegmentationIDMapper

# 导入SimplerEnv
sys.path.append("/home/<USER>/claude/SpatialVLA")
import simpler_env
from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict

class SceneSegmentationAnalyzer:
    """场景分割分析器"""
    
    def __init__(self, output_dir: str = None):
        if output_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = f"scene_segmentation_analysis_{timestamp}"
        
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化分割ID映射器
        self.segmentation_mapper = SegmentationIDMapper(
            output_dir=os.path.join(output_dir, "segmentation_results")
        )
        
        print(f"📁 输出目录: {self.output_dir}")
    
    def create_custom_scene(self, 
                          objects: List[str],
                          positions: List[Tuple[float, float, float]],
                          robot_type: str = "google_robot_static",
                          camera_type: str = "overhead_camera",
                          lighting_mode: str = "indoor_bright",
                          num_episodes: int = 1,
                          verbose: bool = True) -> Tuple[bool, Dict, List[str]]:
        """创建自定义场景"""
        
        print(f"🎬 开始创建自定义场景...")
        print(f"   物体: {objects}")
        print(f"   位置: {positions}")
        print(f"   机器人类型: {robot_type}")
        print(f"   相机类型: {camera_type}")
        print(f"   光照模式: {lighting_mode}")
        
        # 调用场景创建接口
        success, results, images = create_scene_with_custom_positions(
            objects=objects,
            positions=positions,
            robot_type=robot_type,
            camera_type=camera_type,
            lighting_mode=lighting_mode,
            output_dir=self.output_dir,
            image_prefix="scene_analysis",
            num_episodes=num_episodes,
            verbose=verbose
        )
        
        if success:
            print(f"✅ 场景创建成功!")
            print(f"   生成的图像: {images}")
        else:
            print(f"❌ 场景创建失败!")
        
        return success, results, images
    
    def analyze_scene_segmentation(self, 
                                 task_name: str = "google_robot_pick_coke_can",
                                 seed: int = 1234,
                                 camera_name: str = "overhead_camera") -> Dict:
        """分析场景中的分割结果"""
        
        print(f"🔍 开始分析场景分割...")
        print(f"   任务: {task_name}")
        print(f"   种子: {seed}")
        print(f"   相机: {camera_name}")
        
        # 使用分割映射器分析
        try:
            mapping_results = self.segmentation_mapper.run_analysis(
                task_name=task_name,
                seed=seed,
                camera_name=camera_name
            )
            
            print(f"✅ 分割分析完成!")
            return mapping_results
            
        except Exception as e:
            print(f"❌ 分割分析失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def run_complete_analysis(self,
                            objects: List[str],
                            positions: List[Tuple[float, float, float]],
                            robot_type: str = "google_robot_static",
                            camera_type: str = "overhead_camera",
                            lighting_mode: str = "indoor_bright",
                            task_name: str = "google_robot_pick_coke_can",
                            seed: int = 1234,
                            verbose: bool = True) -> Dict:
        """运行完整的场景创建和分割分析"""
        
        print("🚀 开始完整的场景分割分析流程")
        print("=" * 60)
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "scene_creation": {},
            "segmentation_analysis": {},
            "output_dir": self.output_dir,
            "parameters": {
                "objects": objects,
                "positions": positions,
                "robot_type": robot_type,
                "camera_type": camera_type,
                "lighting_mode": lighting_mode,
                "task_name": task_name,
                "seed": seed
            }
        }
        
        # 步骤1: 创建自定义场景
        print("\n📍 步骤1: 创建自定义场景")
        scene_success, scene_results, scene_images = self.create_custom_scene(
            objects=objects,
            positions=positions,
            robot_type=robot_type,
            camera_type=camera_type,
            lighting_mode=lighting_mode,
            verbose=verbose
        )
        
        results["scene_creation"] = {
            "success": scene_success,
            "results": scene_results,
            "images": scene_images
        }
        
        if not scene_success:
            print("❌ 场景创建失败，终止分析")
            return results
        
        # 步骤2: 分析场景分割
        print("\n📍 步骤2: 分析场景分割")
        
        # 相机名称映射
        camera_name_mapping = {
            "overhead_camera": "overhead_camera",
            "base_camera": "base_camera",
            "3rd_view_camera": "3rd_view_camera"
        }
        
        segmentation_camera = camera_name_mapping.get(camera_type, "overhead_camera")
        
        segmentation_results = self.analyze_scene_segmentation(
            task_name=task_name,
            seed=seed,
            camera_name=segmentation_camera
        )
        
        results["segmentation_analysis"] = segmentation_results
        
        # 步骤3: 保存综合结果
        print("\n📍 步骤3: 保存综合结果")
        self.save_complete_results(results)
        
        # 步骤4: 生成总结报告
        print("\n📍 步骤4: 生成总结报告")
        self.generate_summary_report(results)
        
        print("\n✅ 完整的场景分割分析流程完成!")
        print(f"📁 结果保存在: {self.output_dir}")
        
        return results
    
    def save_complete_results(self, results: Dict):
        """保存完整的分析结果"""
        try:
            # 保存JSON格式结果
            json_file = os.path.join(self.output_dir, "complete_analysis_results.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"📄 完整结果已保存到: {json_file}")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
    
    def generate_summary_report(self, results: Dict):
        """生成总结报告"""
        try:
            report_file = os.path.join(self.output_dir, "analysis_summary.txt")
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("场景分割分析总结报告\n")
                f.write("=" * 50 + "\n\n")
                
                f.write(f"分析时间: {results['timestamp']}\n")
                f.write(f"输出目录: {results['output_dir']}\n\n")
                
                # 参数信息
                f.write("分析参数:\n")
                f.write("-" * 20 + "\n")
                params = results['parameters']
                f.write(f"物体: {params['objects']}\n")
                f.write(f"位置: {params['positions']}\n")
                f.write(f"机器人类型: {params['robot_type']}\n")
                f.write(f"相机类型: {params['camera_type']}\n")
                f.write(f"光照模式: {params['lighting_mode']}\n")
                f.write(f"任务名称: {params['task_name']}\n")
                f.write(f"随机种子: {params['seed']}\n\n")
                
                # 场景创建结果
                f.write("场景创建结果:\n")
                f.write("-" * 20 + "\n")
                scene_result = results['scene_creation']
                f.write(f"创建成功: {scene_result['success']}\n")
                if scene_result['success']:
                    f.write(f"生成图像: {scene_result['images']}\n")
                f.write("\n")
                
                # 分割分析结果
                f.write("分割分析结果:\n")
                f.write("-" * 20 + "\n")
                seg_result = results['segmentation_analysis']
                if seg_result:
                    f.write(f"分析成功: True\n")
                    f.write(f"详细结果请查看: {os.path.join(self.output_dir, 'segmentation_results')}\n")
                else:
                    f.write(f"分析成功: False\n")
                
                f.write("\n")
                f.write("=" * 50 + "\n")
                f.write("报告生成完成\n")
            
            print(f"📋 总结报告已生成: {report_file}")
            
        except Exception as e:
            print(f"❌ 生成报告失败: {e}")


def demo_basic_scene():
    """演示基本场景分割分析"""
    print("🎯 演示1: 基本场景分割分析")
    print("=" * 50)
    
    # 创建分析器
    analyzer = SceneSegmentationAnalyzer()
    
    # 定义场景
    objects = ["coke_can", "orange", "apple"]
    positions = [
        (-0.25, 0.15, 0),    # 中心
        (-0.28, 0.12, 0),    # 左下
        (-0.22, 0.18, 0),    # 右上
    ]
    
    # 运行分析
    results = analyzer.run_complete_analysis(
        objects=objects,
        positions=positions,
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="indoor_bright",
        task_name="google_robot_pick_coke_can",
        verbose=True
    )
    
    return results


def demo_multi_object_scene():
    """演示多物体场景分割分析"""
    print("🎯 演示2: 多物体场景分割分析")
    print("=" * 50)
    
    # 创建分析器
    analyzer = SceneSegmentationAnalyzer()
    
    # 定义更复杂的场景
    objects = ["pepsi_can", "coke_can", "orange", "apple", "green_cube_3cm"]
    
    # 使用网格位置
    positions = generate_grid_positions(len(objects))
    
    # 运行分析
    results = analyzer.run_complete_analysis(
        objects=objects,
        positions=positions,
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="laboratory",
        task_name="google_robot_pick_coke_can",
        verbose=True
    )
    
    return results


def demo_circle_layout_scene():
    """演示圆形布局场景分割分析"""
    print("🎯 演示3: 圆形布局场景分割分析")
    print("=" * 50)
    
    # 创建分析器
    analyzer = SceneSegmentationAnalyzer()
    
    # 定义圆形布局场景
    objects = ["coke_can", "pepsi_can", "orange", "apple"]
    
    # 使用圆形位置
    positions = generate_circle_positions(len(objects), radius=0.06)
    
    # 运行分析
    results = analyzer.run_complete_analysis(
        objects=objects,
        positions=positions,
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="natural",
        task_name="google_robot_pick_coke_can",
        verbose=True
    )
    
    return results


def main():
    """主函数"""
    print("🎊 场景分割分析工具")
    print("=" * 60)
    print("本工具结合了场景定义和分割分析功能")
    print("可以在自定义场景中运行分割分析")
    print()
    
    # 运行演示
    demos = [
        ("基本场景", demo_basic_scene),
        ("多物体场景", demo_multi_object_scene),
        ("圆形布局场景", demo_circle_layout_scene),
    ]
    
    print("📋 可用演示:")
    for i, (name, _) in enumerate(demos, 1):
        print(f"   {i}. {name}")
    
    print("\n🚀 开始运行演示...\n")
    
    results = {}
    
    for demo_name, demo_func in demos:
        print(f"\n{'='*60}")
        print(f"开始运行: {demo_name}")
        print(f"{'='*60}")
        
        try:
            result = demo_func()
            results[demo_name] = {
                "success": result.get("scene_creation", {}).get("success", False),
                "output_dir": result.get("output_dir", ""),
                "error": None
            }
            
        except Exception as e:
            print(f"❌ {demo_name} 运行失败: {e}")
            results[demo_name] = {
                "success": False,
                "output_dir": "",
                "error": str(e)
            }
    
    # 总结
    print("\n" + "="*60)
    print("📊 运行总结:")
    
    total_demos = len(results)
    success_demos = sum(1 for r in results.values() if r["success"])
    
    for demo_name, result in results.items():
        status = "✅ 成功" if result["success"] else "❌ 失败"
        print(f"   {demo_name}: {status}")
        if result["success"]:
            print(f"      输出目录: {result['output_dir']}")
        if result["error"]:
            print(f"      错误: {result['error']}")
    
    print(f"\n总计: {success_demos}/{total_demos} 演示成功")
    print(f"成功率: {success_demos/total_demos*100:.1f}%")
    
    print("\n🎉 演示完成!")
    print("\n💡 使用说明:")
    print("   1. 创建SceneSegmentationAnalyzer实例")
    print("   2. 定义物体和位置")
    print("   3. 调用run_complete_analysis()运行分析")
    print("   4. 查看输出目录中的结果")
    print("\n📁 结果文件:")
    print("   • complete_analysis_results.json - 完整的分析结果")
    print("   • analysis_summary.txt - 分析总结报告")
    print("   • segmentation_results/ - 分割分析详细结果")


if __name__ == "__main__":
    main() 