#!/usr/bin/env python3
"""
比较修改前后的苹果图像大小
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 图像路径
before_path = "/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/overhead_rendering_results_20250713_142225/apple/apple_overhead_original.png"
after_path = "/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/overhead_rendering_results_20250713_142533/apple/apple_overhead_original.png"

# 读取图像
before_img = cv2.imread(before_path)
after_img = cv2.imread(after_path)

if before_img is None or after_img is None:
    print("❌ 无法读取图像文件")
    exit(1)

# 转换为RGB格式
before_img_rgb = cv2.cvtColor(before_img, cv2.COLOR_BGR2RGB)
after_img_rgb = cv2.cvtColor(after_img, cv2.COLOR_BGR2RGB)

# 创建比较图
fig, axes = plt.subplots(1, 2, figsize=(12, 6))

# 显示修改前的图像
axes[0].imshow(before_img_rgb)
axes[0].set_title('修改前的苹果 (Scale: 1.0)', fontsize=14)
axes[0].axis('off')

# 显示修改后的图像
axes[1].imshow(after_img_rgb)
axes[1].set_title('修改后的苹果 (Scale: 2.0)', fontsize=14)
axes[1].axis('off')

plt.tight_layout()
plt.suptitle('苹果大小对比', fontsize=16, y=0.98)

# 保存比较图
output_path = "/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/apple_size_comparison.png"
plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"✅ 比较图已保存至: {output_path}")

# 显示一些统计信息
print(f"\n📊 图像信息:")
print(f"修改前图像尺寸: {before_img.shape}")
print(f"修改后图像尺寸: {after_img.shape}")

# 计算图像差异
diff = cv2.absdiff(before_img_rgb, after_img_rgb)
diff_mean = np.mean(diff)
print(f"平均像素差异: {diff_mean:.2f}")

plt.show() 