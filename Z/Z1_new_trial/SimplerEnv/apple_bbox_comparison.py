#!/usr/bin/env python3
"""
比较不同修改方式的苹果效果
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 图像路径
original_path = "/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/overhead_rendering_results_20250713_142225/apple/apple_overhead_original.png"
scaled_path = "/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/half_apple_test/apple_overhead_original.png"
bbox_path = "/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/bbox_half_apple_test/apple_overhead_original.png"

# 读取图像
original_img = cv2.imread(original_path)
scaled_img = cv2.imread(scaled_path)
bbox_img = cv2.imread(bbox_path)

if original_img is None or scaled_img is None or bbox_img is None:
    print("❌ 无法读取图像文件")
    exit(1)

# 转换为RGB格式
original_rgb = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB)
scaled_rgb = cv2.cvtColor(scaled_img, cv2.COLOR_BGR2RGB)
bbox_rgb = cv2.cvtColor(bbox_img, cv2.COLOR_BGR2RGB)

# 创建比较图
fig, axes = plt.subplots(1, 3, figsize=(18, 6))

# 显示原始苹果
axes[0].imshow(original_rgb)
axes[0].set_title('Original Apple\n(bbox: [-0.04, 0.04], scale: 1.0)', fontsize=12)
axes[0].axis('off')

# 显示缩放后的苹果
axes[1].imshow(scaled_rgb)
axes[1].set_title('Scaled Apple\n(bbox: [-0.04, 0.04], scale: 0.5)', fontsize=12)
axes[1].axis('off')

# 显示bbox修改后的苹果
axes[2].imshow(bbox_rgb)
axes[2].set_title('BBox Modified Apple\n(bbox: [0.0, 0.04], scale: 1.0)', fontsize=12)
axes[2].axis('off')

plt.tight_layout()
plt.suptitle('Apple Modification Comparison', fontsize=16, y=0.98)

# 保存比较图
output_path = "/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/apple_bbox_comparison.png"
plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"✅ 比较图已保存至: {output_path}")

# 显示统计信息
print(f"\n📊 图像信息:")
print(f"原始苹果尺寸: {original_img.shape}")
print(f"缩放苹果尺寸: {scaled_img.shape}")
print(f"bbox修改苹果尺寸: {bbox_img.shape}")

# 计算图像差异
diff_scaled = cv2.absdiff(original_rgb, scaled_rgb)
diff_bbox = cv2.absdiff(original_rgb, bbox_rgb)

print(f"原始 vs 缩放 - 平均像素差异: {np.mean(diff_scaled):.2f}")
print(f"原始 vs bbox修改 - 平均像素差异: {np.mean(diff_bbox):.2f}")

plt.show() 