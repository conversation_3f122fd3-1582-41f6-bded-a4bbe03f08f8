#!/usr/bin/env python3
"""
SpatialVLA自定义环境简化测试

专门测试一个自定义环境的完整集成流程
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 设置GPU
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

def setup_custom_environment():
    """设置自定义环境路径和配置"""
    print("🔧 设置自定义环境...")
    
    # 确保使用本地实验版本
    current_dir = Path(__file__).parent.absolute()
    sys.path.insert(0, str(current_dir))
    sys.path.insert(0, str(current_dir / "ManiSkill2_real2sim"))
    
    # 移除系统版本路径以避免冲突
    paths_to_remove = [path for path in sys.path if 'SimplerEnv-OpenVLA' in path]
    for path in paths_to_remove:
        sys.path.remove(path)
        print(f"移除系统路径: {path}")
    
    # 设置资源目录
    os.environ['MS2_REAL2SIM_ASSET_DIR'] = str(current_dir / "ManiSkill2_real2sim" / "data")
    print(f"设置资源目录: {os.environ['MS2_REAL2SIM_ASSET_DIR']}")
    
    return True

def save_video(images, output_path, fps=10):
    """保存视频文件"""
    if not images:
        return
    
    height, width, layers = images[0].shape
    size = (width, height)
    out = cv2.VideoWriter(output_path, cv2.VideoWriter_fourcc(*'mp4v'), fps, size)
    for image in images:
        bgr_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
        out.write(bgr_image)
    out.release()

def main():
    """主函数"""
    print("🚀 SpatialVLA自定义环境简化测试")
    print("=" * 50)
    
    # 设置环境
    if not setup_custom_environment():
        print("❌ 环境设置失败")
        return False
    
    try:
        # 导入必要模块
        print("📦 导入模块...")
        import mani_skill2_real2sim.envs
        from mani_skill2_real2sim.envs.custom_scenes import diverse_scene_env
        import simpler_env
        from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict
        import sapien.core as sapien
        
        # 关闭降噪以避免内核崩溃
        sapien.render_config.rt_use_denoiser = False
        
        # 测试单个自定义环境
        task_name = "custom_diverse_pick_scene"

        print(f"\n🧪 测试改进的物体稳定放置算法")
        print(f"任务: {task_name}")
        print("=" * 60)
        print(f"\n🎯 测试任务: {task_name}")
        
        # SpatialVLA模型配置
        model_name = "spatialvla"
        ckpt_path = "IPEC-COMMUNITY/spatialvla-4b-224-pt"
        action_ensemble_temp = -0.8
        max_timestep = 50  # 减少步数用于快速测试
        
        # 创建输出目录
        output_dir = Path("outputs_simple_test")
        output_dir.mkdir(exist_ok=True)
        
        # 确定策略设置
        policy_setup = "widowx_bridge"
        
        # 加载SpatialVLA模型
        print("🤖 加载SpatialVLA模型...")
        from simpler_env.policies.spatialvla.spatialvla_model import SpatialVLAInference
        
        model = SpatialVLAInference(
            saved_model_path=ckpt_path,
            policy_setup=policy_setup,
            action_scale=1.0,
            action_ensemble_temp=action_ensemble_temp
        )
        print("✅ 模型加载成功")
        
        # 测试3个episodes
        results = []
        for episode in range(3):
            print(f"\n📝 Episode {episode+1}/3")
            
            try:
                # 创建环境
                if 'env' in locals():
                    env.close()
                    del env
                
                env = simpler_env.make(task_name)
                
                # 重置环境
                seed = episode * 1234
                obs, reset_info = env.reset(seed=seed)
                instruction = env.get_language_instruction()
                model.reset(instruction)
                
                print(f"  指令: {instruction}")
                print(f"  重置信息: {reset_info}")
                
                # 获取初始图像
                image = get_image_from_maniskill2_obs_dict(env, obs)
                images = [image]
                
                # 执行推理
                predicted_terminated, success, truncated = False, False, False
                timestep = 0
                
                print(f"  开始推理...")
                while not (success or predicted_terminated or truncated):
                    # 模型推理
                    raw_action, action = model.step(image, instruction)
                    predicted_terminated = bool(action["terminate_episode"][0] > 0)
                    
                    # 执行动作
                    obs, reward, success, truncated, info = env.step(
                        np.concatenate([action["world_vector"], action["rot_axangle"], action["gripper"]])
                    )
                    
                    # 更新观察
                    image = get_image_from_maniskill2_obs_dict(env, obs)
                    images.append(image)
                    timestep += 1
                    
                    if timestep >= max_timestep:
                        break
                    
                    # 每10步打印一次进度
                    if timestep % 10 == 0:
                        print(f"    步数: {timestep}, 奖励: {reward:.3f}")
                
                # 记录结果
                episode_result = {
                    'episode': episode + 1,
                    'success': success,
                    'timesteps': timestep,
                    'terminated': predicted_terminated,
                    'instruction': instruction,
                    'final_reward': reward if 'reward' in locals() else 0.0
                }
                results.append(episode_result)
                
                result_msg = f"  结果: Success={success}, Steps={timestep}, Terminated={predicted_terminated}, Reward={episode_result['final_reward']:.3f}"
                print(result_msg)
                
                # 保存视频
                video_path = output_dir / f"{model_name}_{task_name}_episode{episode+1}.mp4"
                save_video(images, str(video_path))
                print(f"  视频保存: {video_path}")
                
            except Exception as e:
                print(f"  ❌ Episode {episode+1} 失败: {e}")
                import traceback
                traceback.print_exc()
                continue
        
        # 计算总体统计
        if results:
            success_count = sum(1 for r in results if r['success'])
            avg_timesteps = np.mean([r['timesteps'] for r in results])
            success_rate = success_count / len(results)
            
            print(f"\n📊 总体统计:")
            print(f"  成功率: {success_rate:.2%} ({success_count}/{len(results)})")
            print(f"  平均步数: {avg_timesteps:.1f}")
            print(f"  完成的episodes: {len(results)}")
            
            # 保存结果
            results_file = output_dir / "test_results.txt"
            with open(results_file, 'w', encoding='utf-8') as f:
                f.write("SpatialVLA自定义环境简化测试结果\n")
                f.write("=" * 40 + "\n\n")
                f.write(f"任务: {task_name}\n")
                f.write(f"模型: {model_name}\n")
                f.write(f"成功率: {success_rate:.2%}\n")
                f.write(f"平均步数: {avg_timesteps:.1f}\n\n")
                
                for result in results:
                    f.write(f"Episode {result['episode']}: {result}\n")
            
            print(f"  结果保存: {results_file}")
        
        # 清理
        if 'env' in locals():
            env.close()
        del model
        
        print(f"\n✅ 简化测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("🎉 SpatialVLA自定义环境集成测试成功!")
    else:
        print("💥 测试失败，请检查错误信息")
