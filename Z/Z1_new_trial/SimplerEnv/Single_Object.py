#!/usr/bin/env python3
"""
Custom Object Renderer with Overhead Camera

Specialized overhead camera visualization for objects using custom ManiSkill2 environments
Integrated segmentation ID mapping to ensure proper object identification
"""

import os
import sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
import matplotlib
from pathlib import Path
from datetime import datetime
import json
import logging
from typing import Dict, List, Tuple, Optional, Any
import gymnasium as gym

# Setup matplotlib for English display
try:
    matplotlib.rcParams['font.family'] = 'sans-serif'
    matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Liberation Sans']
    matplotlib.rcParams['axes.unicode_minus'] = False
except:
    pass

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add paths
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# Import ManiSkill2 environments
from mani_skill2_real2sim.envs.custom_scenes import *

# Set environment variables
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")


class CustomSegmentationProcessor:
    """Custom Segmentation Processor for ManiSkill2 environments"""
    
    def __init__(self):
        self.id_mapping = {}
    
    def get_actors_info(self, env) -> List[Dict[str, Any]]:
        """Get information about all actors in the environment"""
        actors_info = []
        
        try:
            actors = env.get_actors()
            logger.info(f"Found {len(actors)} actors")
            
            for actor in actors:
                actor_info = {
                    'id': getattr(actor, 'id', 'unknown'),
                    'name': getattr(actor, 'name', 'unknown'),
                    'type': type(actor).__name__,
                    'source': 'env.get_actors()'
                }
                actors_info.append(actor_info)
                logger.info(f"Actor: ID={actor_info['id']}, Name='{actor_info['name']}'")
                    
        except Exception as e:
            logger.error(f"Error getting actors info: {e}")
        
        return actors_info
    
    def get_articulations_info(self, env) -> List[Dict[str, Any]]:
        """Get information about all articulations in the environment"""
        articulations_info = []
        
        try:
            articulations = env.get_articulations()
            logger.info(f"Found {len(articulations)} articulations")
            
            for articulation in articulations:
                links = []
                if hasattr(articulation, 'get_links'):
                    for link in articulation.get_links():
                        link_info = {
                            'id': getattr(link, 'id', 'unknown'),
                            'name': getattr(link, 'name', 'unknown'),
                            'type': type(link).__name__
                        }
                        links.append(link_info)
                
                articulation_info = {
                    'id': getattr(articulation, 'id', 'unknown'),
                    'name': getattr(articulation, 'name', 'unknown'),
                    'type': type(articulation).__name__,
                    'links': links,
                    'source': 'env.get_articulations()'
                }
                articulations_info.append(articulation_info)
                logger.info(f"Articulation: ID={articulation_info['id']}, Name='{articulation_info['name']}', Links={len(links)}")
                    
        except Exception as e:
            logger.error(f"Error getting articulations info: {e}")
        
        return articulations_info
    
    def analyze_segmentation_data(self, obs, camera_name: str = "overhead_camera") -> Dict[str, Any]:
        """Analyze segmentation data"""
        analysis = {
            "camera_name": camera_name,
            "segmentation_stats": {},
            "id_distribution": {},
            "image_info": {},
            "error": None
        }
        
        try:
            if 'image' not in obs or camera_name not in obs['image']:
                analysis["error"] = f"Camera {camera_name} not found"
                return analysis
            
            camera_data = obs['image'][camera_name]
            
            # Find segmentation data
            segmentation = None
            seg_keys = ['Segmentation', 'segmentation', 'seg']
            
            for key in seg_keys:
                if key in camera_data:
                    segmentation = camera_data[key]
                    break
            
            if segmentation is None:
                analysis["error"] = f"No segmentation data found in camera {camera_name}"
                logger.warning(f"Available keys: {list(camera_data.keys())}")
                return analysis
            
            # Analyze segmentation data
            if len(segmentation.shape) == 3:
                # Usually segmentation data is in the second channel
                seg_channel = segmentation[..., 1] if segmentation.shape[2] > 1 else segmentation[..., 0]
            else:
                seg_channel = segmentation
            
            # Count unique IDs
            unique_ids, counts = np.unique(seg_channel, return_counts=True)
            
            analysis["image_info"] = {
                "shape": segmentation.shape,
                "dtype": str(segmentation.dtype),
                "value_range": [int(segmentation.min()), int(segmentation.max())]
            }
            
            analysis["segmentation_stats"] = {
                "unique_id_count": len(unique_ids),
                "unique_id_list": [int(id_val) for id_val in unique_ids],
                "total_pixels": int(seg_channel.size)
            }
            
            # ID distribution analysis
            id_distribution = {}
            for id_val, count in zip(unique_ids, counts):
                percentage = (count / seg_channel.size) * 100
                id_distribution[int(id_val)] = {
                    "pixel_count": int(count),
                    "percentage": f"{percentage:.2f}%"
                }
            
            analysis["id_distribution"] = id_distribution
            
            logger.info(f"Segmentation analysis complete: found {len(unique_ids)} unique IDs")
            
        except Exception as e:
            analysis["error"] = str(e)
            logger.error(f"Segmentation data analysis failed: {e}")
        
        return analysis
    
    def create_id_mapping(self, actors_info: List[Dict], articulations_info: List[Dict],
                         segmentation_analysis: Dict, target_model_id: str = None) -> Dict[int, Dict[str, Any]]:
        """Create ID mapping"""
        id_mapping = {}
        
        try:
            # Get all IDs from segmentation analysis
            if "segmentation_stats" in segmentation_analysis and "unique_id_list" in segmentation_analysis["segmentation_stats"]:
                unique_ids = segmentation_analysis["segmentation_stats"]["unique_id_list"]
            else:
                logger.warning("No unique ID list found in segmentation analysis")
                return id_mapping
            
            # Create name to info mapping
            name_to_info = {}
            
            # Add actors
            for actor_info in actors_info:
                name = actor_info['name']
                if name != 'unknown':
                    name_to_info[name] = {
                        'type': 'actor',
                        'info': actor_info,
                        'category': self.classify_object_type(name),
                        'is_target': target_model_id and target_model_id in name
                    }
            
            # Add articulations and their links
            for articulation_info in articulations_info:
                name = articulation_info['name']
                if name != 'unknown':
                    name_to_info[name] = {
                        'type': 'articulation',
                        'info': articulation_info,
                        'category': self.classify_object_type(name),
                        'is_target': False
                    }
                
                # Add links
                for link_info in articulation_info.get('links', []):
                    link_name = link_info['name']
                    if link_name != 'unknown':
                        name_to_info[link_name] = {
                            'type': 'link',
                            'info': link_info,
                            'parent': name,
                            'category': self.classify_object_type(link_name),
                            'is_target': False
                        }
            
            # Create mapping for each segmentation ID
            for seg_id in unique_ids:
                id_mapping[seg_id] = {
                    'id': seg_id,
                    'name': 'unknown',
                    'category': 'unknown',
                    'type': 'unknown',
                    'is_target': False,
                    'pixel_info': segmentation_analysis["id_distribution"].get(seg_id, {})
                }
                
                # Try to match by ID
                for name, obj_info in name_to_info.items():
                    try:
                        obj_id = obj_info['info'].get('id', 'unknown')
                        if str(obj_id) == str(seg_id):
                            id_mapping[seg_id].update({
                                'name': name,
                                'category': obj_info['category'],
                                'type': obj_info['type'],
                                'is_target': obj_info['is_target'],
                                'full_info': obj_info['info']
                            })
                            logger.info(f"Mapping successful: ID {seg_id} -> {name} ({obj_info['category']})")
                            break
                    except:
                        continue
            
            self.id_mapping = id_mapping
            logger.info(f"Created {len(id_mapping)} ID mappings")
            
        except Exception as e:
            logger.error(f"ID mapping creation failed: {e}")
        
        return id_mapping
    
    def classify_object_type(self, name: str) -> str:
        """Classify object type"""
        name_lower = name.lower()
        
        # Target objects
        target_keywords = ['can', 'bottle', 'cube', 'apple', 'orange', 'ball', 'box', 'opened']
        if any(keyword in name_lower for keyword in target_keywords):
            return 'target_object'
        
        # Robot parts
        robot_keywords = ['robot', 'arm', 'gripper', 'hand', 'finger', 'link', 'joint']
        if any(keyword in name_lower for keyword in robot_keywords):
            return 'robot_part'
        
        # Environment
        env_keywords = ['ground', 'table', 'floor', 'wall', 'base', 'arena']
        if any(keyword in name_lower for keyword in env_keywords):
            return 'environment'
        
        return 'other'
    
    def create_segmentation_mask(self, obs, camera_name: str, target_categories: List[str] = None) -> np.ndarray:
        """Create segmentation mask, showing only specified categories"""
        if target_categories is None:
            target_categories = ['target_object', 'environment']  # Default: show target objects and environment
        
        try:
            camera_data = obs['image'][camera_name]
            
            # Get segmentation data
            segmentation = None
            seg_keys = ['Segmentation', 'segmentation', 'seg']
            
            for key in seg_keys:
                if key in camera_data:
                    segmentation = camera_data[key]
                    break
            
            if segmentation is None:
                logger.warning("No segmentation data found, returning all-ones mask")
                return np.ones(camera_data.get('Color', camera_data.get('rgb', np.zeros((224, 224, 3)))).shape[:2], dtype=bool)
            
            # Get segmentation channel
            if len(segmentation.shape) == 3:
                seg_channel = segmentation[..., 1] if segmentation.shape[2] > 1 else segmentation[..., 0]
            else:
                seg_channel = segmentation
            
            # Create mask
            mask = np.zeros(seg_channel.shape, dtype=bool)
            
            for seg_id, mapping_info in self.id_mapping.items():
                if mapping_info['category'] in target_categories:
                    mask |= (seg_channel == seg_id)
                    logger.info(f"Added to mask: ID {seg_id} - {mapping_info['name']} ({mapping_info['category']})")
            
            return mask
            
        except Exception as e:
            logger.error(f"Segmentation mask creation failed: {e}")
            # Return all-ones mask as fallback
            return np.ones((224, 224), dtype=bool)


# Object to environment mapping
OBJECT_TO_ENV_MAP = {
    'orange': 'GraspSingleOrangeInScene-v0',
    'apple': 'GraspSingleAppleInScene-v0', 
    'coke_can': 'GraspSingleOpenedCokeCanInScene-v0',
    'pepsi_can': 'GraspSingleOpenedPepsiCanInScene-v0',
    'blue_plastic_bottle': 'GraspSingleBluePlasticBottleInScene-v0',
    'green_cube_3cm': 'GraspSingleCustomInScene-v0',  # Use custom for cubes
    'sponge': 'GraspSingleSpongeInScene-v0',
    '7can_rbcc': 'GraspSingleCustomInScene-v0',  # Custom RBCC can with mask
}


def render_single_object_overhead(model_id: str, output_dir: str):
    """Render single object using overhead camera with custom ManiSkill2 environment - Original images only"""
    print(f"\n🎨 Rendering object: {model_id}")
    print("=" * 40)
    
    try:
        # Get environment name for the object
        env_name = OBJECT_TO_ENV_MAP.get(model_id, 'GraspSingleCustomInScene-v0')
        print(f"Using environment: {env_name}")
        
        # Create custom ManiSkill2 environment
        env = gym.make(
            env_name,
            obs_mode="image",
            control_mode="arm_pd_ee_delta_pose_align_interpolate_by_planner_gripper_pd_joint_target_delta_pos_interpolate_by_planner",
            camera_cfgs={"add_segmentation": True},
            render_mode=None
        )
        print(f"✓ Environment created successfully: {env_name}")
        
        # Reset environment with custom object if needed
        reset_options = {}
        if model_id not in ['orange', 'apple', 'coke_can', 'pepsi_can', 'blue_plastic_bottle', 'sponge']:
            reset_options['model_id'] = model_id
        
        obs, reset_info = env.reset(seed=1234, options=reset_options)
        print(f"✓ Environment reset complete")
        
        # Get object position if available
        try:
            if hasattr(env, 'obj') and env.obj:
                obj = env.obj
                pos = obj.pose.p
                print(f"✓ Object position: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        except:
            print("⚠️  Unable to get object position")
        
        rendered_images = {}
        
        # Use overhead_camera
        if 'image' in obs and 'overhead_camera' in obs['image']:
            print(f"📸 Using overhead_camera for rendering")
            
            camera_data = obs['image']['overhead_camera']
            print(f"📊 Camera data keys: {list(camera_data.keys())}")
            
            # Get RGB image
            rgb_img = None
            for img_key in ['Color', 'rgb', 'color']:
                if img_key in camera_data:
                    rgb_img = camera_data[img_key]
                    break
            
            if rgb_img is not None:
                # Ensure correct image format
                if rgb_img.dtype != np.uint8:
                    rgb_img = (rgb_img * 255).astype(np.uint8)
                
                # Handle RGBA to RGB conversion
                if len(rgb_img.shape) == 3 and rgb_img.shape[2] == 4:
                    # RGBA -> RGB
                    rgb_img = rgb_img[:, :, :3]
                    print(f"  🔄 Converted RGBA to RGB")
                
                # Check if image is valid
                if rgb_img.size > 0 and rgb_img.shape[0] > 0 and rgb_img.shape[1] > 0:
                    
                    # Save only original image
                    original_filename = f"{model_id}_overhead_original.png"
                    original_path = Path(output_dir) / original_filename
                    cv2.imwrite(str(original_path), cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                    rendered_images['original'] = str(original_path)
                    print(f"  ✅ Original overhead image: {original_path}")
                    
                    # Display image dimensions
                    height, width = rgb_img.shape[:2]
                    print(f"  📐 Image dimensions: {width} x {height}")
                    
                else:
                    print(f"  ⚠️  overhead_camera image is invalid")
            else:
                print(f"  ❌ No image data found in overhead_camera")
        else:
            print(f"❌ No overhead_camera data in observations")
            if 'image' in obs:
                print(f"   Available cameras: {list(obs['image'].keys())}")
        
        env.close()
        return rendered_images
        
    except Exception as e:
        print(f"❌ Rendering failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


def get_object_info(env, model_id: str):
    """Get object information"""
    info = {
        "model_id": model_id,
        "timestamp": datetime.now().isoformat(),
        "render_mode": "overhead_camera_with_custom_segmentation",
        "object_properties": {},
        "geometry_info": {},
        "file_info": {},
        "runtime_info": {}
    }
    
    try:
        # Runtime information
        if hasattr(env, 'obj') and env.obj:
            obj = env.obj
            pos = obj.pose.p
            rot = obj.pose.q
            
            info["runtime_info"] = {
                "3d_position": [float(pos[0]), float(pos[1]), float(pos[2])],
                "overhead_projection_position": [float(pos[0]), float(pos[1])],  # X, Y coordinates
                "ground_height": float(pos[2]),
                "rotation_quaternion": [float(rot[0]), float(rot[1]), float(rot[2]), float(rot[3])],
                "object_name": obj.name
            }
    except Exception as e:
        info["error"] = str(e)
    
    return info


def get_available_models():
    """Get available models"""
    # Return supported objects from the environment mapping
    return list(OBJECT_TO_ENV_MAP.keys())


def batch_render_overhead_view(model_list, output_dir):
    """Batch render objects with overhead view - Original images only"""
    print(f"🚀 Starting batch overhead rendering for {len(model_list)} objects")
    print("=" * 50)
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    results = {}
    success_count = 0
    
    for i, model_id in enumerate(model_list, 1):
        print(f"\n📦 [{i}/{len(model_list)}] Processing: {model_id}")
        
        try:
            # Create subdirectory for each object
            model_dir = output_path / model_id
            model_dir.mkdir(exist_ok=True)
            
            # Render object with overhead view
            images = render_single_object_overhead(model_id, str(model_dir))
            
            if images and 'original' in images:
                results[model_id] = {
                    "status": "success",
                    "images": images,
                    "output_dir": str(model_dir)
                }
                success_count += 1
                print(f"✅ {model_id} completed")
            else:
                results[model_id] = {
                    "status": "failed",
                    "error": "No original overhead image generated"
                }
                print(f"❌ {model_id} failed")
        
        except Exception as e:
            results[model_id] = {
                "status": "error", 
                "error": str(e)
            }
            print(f"❌ {model_id} error: {e}")
    
    # Generate summary report
    summary = {
        "overhead_rendering_summary": {
            "render_mode": "overhead_camera_original_only",
            "total": len(model_list),
            "success": success_count,
            "failed": len(model_list) - success_count,
            "success_rate": f"{success_count/len(model_list)*100:.1f}%"
        },
        "detailed_results": results,
        "timestamp": datetime.now().isoformat()
    }
    
    # Save summary
    summary_path = output_path / "overhead_rendering_summary.json"
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 Overhead rendering completed:")
    print(f"   Total: {len(model_list)}")
    print(f"   Success: {success_count}")
    print(f"   Failed: {len(model_list) - success_count}")
    print(f"   Success rate: {success_count/len(model_list)*100:.1f}%")
    print(f"   Results saved in: {output_dir}")
    print(f"   Summary report: {summary_path}")
    
    return results


def main():
    """Main function"""
    print("🔍 Custom Object Renderer with Overhead Camera")
    print("=" * 50)
    print("📹 Simplified overhead camera rendering - Original images only")
    print("=" * 50)
    
    # Get available models
    available_models = get_available_models()
    
    if not available_models:
        print("❌ No available models found")
        return
    
    print(f"🔍 Found {len(available_models)} available models")
    
    # Select models to render
    target_models = available_models[:8]  # First 6 available models
    
    print(f"\n🎯 Objects to be rendered with overhead view:")
    for i, model in enumerate(target_models, 1):
        env_name = OBJECT_TO_ENV_MAP.get(model, 'GraspSingleCustomInScene-v0')
        print(f"   {i}. {model} (Environment: {env_name})")
    
    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/overhead_rendering_results_{timestamp}"
    
    # Start overhead rendering
    results = batch_render_overhead_view(target_models, output_dir)
    
    print(f"\n🎉 Overhead rendering completed! Results saved in: {output_dir}")
    
    return results


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  User interrupted")
    except Exception as e:
        print(f"\n❌ Program error: {e}")
        import traceback
        traceback.print_exc() 