#!/usr/bin/env python3
"""
SpatialVLA自定义环境集成脚本

将SpatialVLA模型集成到自定义SimplerEnv场景中进行推理和评估
"""

import os
import sys
import numpy as np
import cv2
from tqdm import tqdm
import random
from pathlib import Path

# 设置GPU
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

def setup_custom_environment():
    """设置自定义环境路径和配置"""
    print("🔧 设置自定义环境...")
    
    # 确保使用本地实验版本
    current_dir = Path(__file__).parent.absolute()
    sys.path.insert(0, str(current_dir))
    sys.path.insert(0, str(current_dir / "ManiSkill2_real2sim"))
    
    # 移除系统版本路径以避免冲突
    paths_to_remove = [path for path in sys.path if 'SimplerEnv-OpenVLA' in path]
    for path in paths_to_remove:
        sys.path.remove(path)
        print(f"移除系统路径: {path}")
    
    # 设置资源目录
    os.environ['MS2_REAL2SIM_ASSET_DIR'] = str(current_dir / "ManiSkill2_real2sim" / "data")
    print(f"设置资源目录: {os.environ['MS2_REAL2SIM_ASSET_DIR']}")
    
    return True

def save_video(images, output_path, fps=10):
    """保存视频文件"""
    if not images:
        return
    
    height, width, layers = images[0].shape
    size = (width, height)
    out = cv2.VideoWriter(output_path, cv2.VideoWriter_fourcc(*'mp4v'), fps, size)
    for image in images:
        bgr_image = cv2.COLOR_RGB2BGR if len(image.shape) == 3 else cv2.COLOR_GRAY2BGR
        out.write(cv2.cvtColor(image, bgr_image))
    out.release()

def main():
    """主函数"""
    print("🚀 SpatialVLA自定义环境集成测试")
    print("=" * 60)
    
    # 设置环境
    if not setup_custom_environment():
        print("❌ 环境设置失败")
        return False
    
    try:
        # 导入必要模块
        print("📦 导入模块...")
        import mani_skill2_real2sim.envs
        from mani_skill2_real2sim.envs.custom_scenes import diverse_scene_env
        import simpler_env
        from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict
        import sapien.core as sapien
        
        # 关闭降噪以避免内核崩溃
        sapien.render_config.rt_use_denoiser = False
        
        # 自定义环境任务列表
        custom_task_names = [
            "custom_diverse_pick_scene",
            "custom_enhanced_scene", 
            "custom_enhanced_low_diversity",
            "custom_enhanced_medium_diversity",
        ]
        
        # SpatialVLA模型配置
        model_name = "spatialvla"
        ckpt_path = "IPEC-COMMUNITY/spatialvla-4b-224-pt"
        action_ensemble_temp = -0.8
        exp_num = 5  # 减少实验次数用于测试
        seeds = [i * 1234 for i in range(exp_num)]
        max_timestep = 100  # 减少最大步数用于测试
        
        # 创建输出目录
        output_dir = Path("outputs_custom")
        output_dir.mkdir(exist_ok=True)
        
        # 结果记录
        results_file = output_dir / "spatialvla_custom_results.txt"
        
        with open(results_file, 'w') as f:
            f.write("SpatialVLA自定义环境评估结果\n")
            f.write("=" * 50 + "\n\n")
            
            for task_name in custom_task_names:
                print(f"\n🎯 测试任务: {task_name}")
                f.write(f"Task: {task_name}    Model: {model_name}\n")
                
                # 确定策略设置
                if "google" in task_name:
                    policy_setup = "google_robot"
                else:
                    policy_setup = "widowx_bridge"
                
                try:
                    # 加载SpatialVLA模型
                    print("🤖 加载SpatialVLA模型...")
                    from simpler_env.policies.spatialvla.spatialvla_model import SpatialVLAInference
                    
                    model = SpatialVLAInference(
                        saved_model_path=ckpt_path,
                        policy_setup=policy_setup,
                        action_scale=1.0,
                        action_ensemble_temp=action_ensemble_temp
                    )
                    print("✅ 模型加载成功")
                    
                    total_frame, total_fail, total_success = 0, 0, 0
                    
                    for i, seed in zip(range(exp_num), seeds):
                        print(f"  📝 Episode {i+1}/{exp_num} (seed={seed})")
                        
                        # 创建环境
                        if 'env' in locals():
                            env.close()
                            del env
                        
                        env = simpler_env.make(task_name)
                        
                        # 重置环境
                        obs, reset_info = env.reset(seed=seed)
                        instruction = env.get_language_instruction()
                        model.reset(instruction)
                        
                        print(f"    指令: {instruction}")
                        f.write(f"Episode {i+1} - Instruction: {instruction}\n")
                        
                        # 获取初始图像
                        image = get_image_from_maniskill2_obs_dict(env, obs)
                        images = [image]
                        
                        # 执行推理
                        predicted_terminated, success, truncated = False, False, False
                        timestep = 0
                        
                        while not (success or predicted_terminated or truncated):
                            # 模型推理
                            raw_action, action = model.step(image, instruction)
                            predicted_terminated = bool(action["terminate_episode"][0] > 0)
                            
                            # 执行动作
                            obs, reward, success, truncated, info = env.step(
                                np.concatenate([action["world_vector"], action["rot_axangle"], action["gripper"]])
                            )
                            
                            # 更新观察
                            image = get_image_from_maniskill2_obs_dict(env, obs)
                            images.append(image)
                            timestep += 1
                            
                            if timestep >= max_timestep:
                                break
                        
                        # 记录结果
                        total_frame += timestep
                        if timestep == max_timestep:
                            total_fail += 1
                        if success:
                            total_success += 1
                        
                        episode_stats = info.get("episode_stats", {})
                        result_msg = f"Episode {i+1}: Success={success}, Steps={timestep}, Terminated={predicted_terminated}"
                        print(f"    {result_msg}")
                        f.write(f"{result_msg}\n")
                        
                        # 保存视频
                        video_path = output_dir / f"{model_name}_{task_name}_seed{seed}.mp4"
                        save_video(images, str(video_path))
                        
                        f.flush()
                    
                    # 计算统计信息
                    avg_timestep = total_frame / exp_num
                    avg_fail_rate = total_fail / exp_num
                    success_rate = total_success / exp_num
                    
                    stats_msg = f"平均步数: {avg_timestep:.2f}, 失败率: {avg_fail_rate:.2f}, 成功率: {success_rate:.2f}"
                    print(f"  📊 {stats_msg}")
                    f.write(f"Statistics: {stats_msg}\n\n")
                    
                    # 清理模型
                    del model
                    
                except Exception as e:
                    error_msg = f"❌ 任务 {task_name} 执行失败: {e}"
                    print(error_msg)
                    f.write(f"ERROR: {error_msg}\n\n")
                    import traceback
                    traceback.print_exc()
                    continue
            
            f.write("🎉 所有任务完成!\n")
        
        print(f"\n✅ 集成测试完成! 结果保存在: {results_file}")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("🎉 SpatialVLA自定义环境集成成功!")
    else:
        print("💥 集成失败，请检查错误信息")
